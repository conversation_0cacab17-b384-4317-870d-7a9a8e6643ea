"""Download a sample 360 panorama image for testing"""
import urllib.request
import os
from PIL import Image
import numpy as np

def create_test_panorama():
    """Create a test 360 panorama image"""
    # Create a simple test panorama with gradient and markers
    width = 3600  # 10 degrees per 100 pixels
    height = 1800  # 10 degrees per 100 pixels
    
    # Create image array
    img_array = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Sky gradient (top half)
    for y in range(height // 2):
        ratio = y / (height // 2)
        r = int(135 + (185 - 135) * ratio)
        g = int(206 + (235 - 206) * ratio)
        b = int(235 + (255 - 235) * ratio)
        img_array[y, :] = [r, g, b]
    
    # Ground (bottom half)
    for y in range(height // 2, height):
        ratio = (y - height // 2) / (height // 2)
        r = int(150 - 50 * ratio)
        g = int(150 - 50 * ratio)
        b = int(100 - 40 * ratio)
        img_array[y, :] = [r, g, b]
    
    # Add vertical markers every 45 degrees
    for angle in range(0, 360, 45):
        x = int(angle / 360 * width)
        # Draw white line
        img_array[:, x:x+5] = [255, 255, 255]
        
    # Add horizontal line at horizon
    img_array[height//2 - 2:height//2 + 2, :] = [200, 200, 200]
    
    # Add some building silhouettes
    np.random.seed(42)
    for i in range(20):
        x_start = np.random.randint(0, width - 200)
        building_width = np.random.randint(100, 200)
        building_height = np.random.randint(height // 4, height // 2)
        y_start = height - building_height
        
        # Building color
        color = np.random.randint(60, 100, size=3)
        img_array[y_start:height, x_start:x_start+building_width] = color
    
    # Create PIL image and save
    img = Image.fromarray(img_array)
    
    # Ensure assets directory exists
    os.makedirs('assets', exist_ok=True)
    
    # Save the image
    img.save('assets/balcony_360.jpg', 'JPEG', quality=90)
    print("Created test panorama at assets/balcony_360.jpg")
    print(f"Image size: {width}x{height}")

if __name__ == "__main__":
    create_test_panorama() 