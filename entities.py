"""Game entities: Pigeons and Projectiles"""

import numpy as np
import random
import math
from dataclasses import dataclass
from typing import List, Tuple, Optional, Dict, Any
from loguru import logger as log
import time


@dataclass
class Entity3D:
    """Base class for 3D entities"""

    position: np.ndarray  # [x, y, z] in world space
    velocity: np.ndarray  # [vx, vy, vz]
    size: float
    active: bool

    def update(self, dt: float):
        """Update entity position"""
        if self.active:
            self.position += self.velocity * dt


@dataclass
class Pigeon(Entity3D):
    """Flying pigeon entity"""

    id: int
    spawn_time: float
    hit_time: Optional[float] = None
    is_fleeing: bool = False
    shots_taken_at: int = 0  # Number of shots fired at this pigeon
    last_in_range_time: float = 0.0  # Last time pigeon was in engagement range
    crosses_range: bool = False  # Whether this pigeon's path crosses turret range

    @classmethod
    def spawn_random(
        cls,
        pigeon_id: int,
        spawn_time: float,
        distance_range: Tuple[float, float] = (300, 1000),
        height_range: Tuple[float, float] = (-200, 400),
        speed_range: Tuple[float, float] = (50, 150),
        ensure_relevance: bool = True,
    ) -> "Pigeon":
        """Spawn a pigeon at random position with relevant flight path"""
        from config import CONFIG

        max_attempts = 10
        for attempt in range(max_attempts):
            # Random angle around the camera
            angle = random.uniform(0, 360)
            angle_rad = math.radians(angle)

            # Random distance and height - spawn within range for relevance
            if ensure_relevance:
                # Spawn closer to ensure they're immediately relevant
                distance = random.uniform(
                    distance_range[0],
                    min(
                        distance_range[1],
                        CONFIG.targeting.MAX_ENGAGEMENT_DISTANCE * 0.9,
                    ),
                )
            else:
                distance = random.uniform(*distance_range)
            height = random.uniform(*height_range)

            # Calculate position
            x = distance * math.sin(angle_rad)
            z = distance * math.cos(angle_rad)
            y = height
            position = np.array([x, y, z], dtype=float)

            # 3D movement trajectory - ensure it crosses turret range
            speed = random.uniform(*speed_range)

            if ensure_relevance:
                # Calculate direction that will cross through turret range
                # Aim roughly toward origin with some randomness
                target_angle = (
                    angle_rad + math.pi + random.uniform(-math.pi / 3, math.pi / 3)
                )
                direction_yaw = target_angle
                direction_pitch = random.uniform(-math.pi / 12, math.pi / 12)
            else:
                # Random 3D direction with less vertical variation
                direction_yaw = random.uniform(0, 2 * math.pi)
                direction_pitch = random.uniform(-math.pi / 12, math.pi / 12)

            vx = speed * math.cos(direction_pitch) * math.sin(direction_yaw)
            vy = speed * math.sin(direction_pitch) * 0.5  # Reduced vertical component
            vz = speed * math.cos(direction_pitch) * math.cos(direction_yaw)

            velocity = np.array([vx, vy, vz], dtype=float)

            # Check if this trajectory will cross turret range
            crosses_range = cls._will_cross_range(
                position, velocity, CONFIG.targeting.MAX_ENGAGEMENT_DISTANCE
            )

            if not ensure_relevance or crosses_range:
                pigeon = cls(
                    position=position,
                    velocity=velocity,
                    size=30,
                    active=True,
                    id=pigeon_id,
                    spawn_time=spawn_time,
                    last_in_range_time=spawn_time
                    if distance <= CONFIG.targeting.MAX_ENGAGEMENT_DISTANCE
                    else 0.0,
                    crosses_range=crosses_range,
                )
                return pigeon

        # Fallback: create pigeon without relevance check
        return cls.spawn_random(
            pigeon_id, spawn_time, distance_range, height_range, speed_range, False
        )

    @staticmethod
    def _will_cross_range(
        position: np.ndarray, velocity: np.ndarray, max_range: float
    ) -> bool:
        """Check if pigeon trajectory will cross within turret range"""
        # Simple check: will the pigeon get closer to origin?
        current_distance = np.linalg.norm(position)

        # Project position forward in time
        for t in [1.0, 2.0, 3.0, 5.0]:  # Check at multiple time points
            future_pos = position + velocity * t
            future_distance = np.linalg.norm(future_pos)
            if future_distance < max_range:
                return True

        return current_distance <= max_range

    def update_range_status(self, current_time: float, max_range: float):
        """Update whether pigeon is currently in range"""
        distance = np.linalg.norm(self.position)
        if distance <= max_range:
            self.last_in_range_time = current_time

    def is_out_of_range_too_long(
        self, current_time: float, timeout: float, max_range: float
    ) -> bool:
        """Check if pigeon has been out of range for too long"""
        distance = np.linalg.norm(self.position)
        if distance <= max_range:
            return False  # Currently in range

        return (current_time - self.last_in_range_time) > timeout

    def start_fleeing(self, turret_position: np.ndarray = np.array([0, 0, 0])):
        """Make pigeon flee away from turret at maximum speed"""
        if not self.is_fleeing:
            self.is_fleeing = True

            # Calculate direction away from turret
            flee_direction = self.position - turret_position
            distance = np.linalg.norm(flee_direction)

            if distance > 0:
                flee_direction = flee_direction / distance
                # Set velocity to flee at maximum speed
                from config import CONFIG

                self.velocity = flee_direction * CONFIG.environment.PIGEON_FLEE_SPEED

                # Add some random vertical component for realistic escape
                self.velocity[1] += random.uniform(-50, 100)  # Upward bias

    def get_apparent_size(self, base_size: float = 30.0) -> float:
        """Calculate apparent size based on distance from origin"""
        distance = np.linalg.norm(self.position)
        # Size decreases with distance, minimum size of 5
        apparent_size = max(5, int(base_size * 1000 / max(distance, 100)))
        return min(apparent_size, 50)  # Cap at 50 pixels

    def to_spherical(self) -> Tuple[float, float, float]:
        """Convert position to spherical coordinates (yaw, pitch, distance)"""
        x, y, z = self.position
        distance = np.linalg.norm(self.position)

        if distance < 0.001:
            return 0, 0, 0

        # Yaw angle (horizontal)
        yaw = math.degrees(math.atan2(x, z)) % 360

        # Pitch angle (vertical)
        pitch = math.degrees(math.asin(np.clip(y / distance, -1, 1)))

        return yaw, pitch, distance


@dataclass
class Projectile(Entity3D):
    """Fired projectile"""

    launch_time: float
    target_id: Optional[int] = None
    hit: bool = False

    @classmethod
    def fire_at_position(
        cls, launch_time: float, target_pos: np.ndarray, projectile_speed: float
    ) -> "Projectile":
        """Create a projectile aimed at target position"""
        # Start at origin (turret position)
        position = np.array([0.0, 0.0, 0.0])

        # Calculate velocity towards target
        direction = target_pos - position
        distance = np.linalg.norm(direction)

        if distance > 0:
            direction = direction / distance
            velocity = direction * projectile_speed
        else:
            velocity = np.array([0.0, 0.0, 0.0])

        return cls(
            position=position,
            velocity=velocity,
            size=5,
            active=True,
            launch_time=launch_time,
        )

    @classmethod
    def fire_with_prediction(
        cls, launch_time: float, pigeon: Pigeon, projectile_speed: float
    ) -> "Projectile":
        """Create a projectile with predictive targeting"""
        # Calculate intercept point
        intercept_pos = cls._calculate_intercept(
            pigeon.position, pigeon.velocity, projectile_speed
        )

        projectile = cls.fire_at_position(launch_time, intercept_pos, projectile_speed)
        projectile.target_id = pigeon.id
        return projectile

    @staticmethod
    def _calculate_intercept(
        target_pos: np.ndarray, target_vel: np.ndarray, projectile_speed: float
    ) -> np.ndarray:
        """Calculate intercept point for moving target"""
        # Solve quadratic equation for intercept time
        # |target_pos + target_vel * t| = projectile_speed * t

        a = np.dot(target_vel, target_vel) - projectile_speed**2
        b = 2 * np.dot(target_pos, target_vel)
        c = np.dot(target_pos, target_pos)

        if abs(a) < 0.001:
            # Linear case
            if abs(b) > 0.001:
                t = -c / b
            else:
                t = 0
        else:
            # Quadratic case
            discriminant = b * b - 4 * a * c
            if discriminant < 0:
                # No solution, aim at current position
                return target_pos

            t1 = (-b + math.sqrt(discriminant)) / (2 * a)
            t2 = (-b - math.sqrt(discriminant)) / (2 * a)

            # Choose positive minimum time
            valid_times = [t for t in [t1, t2] if t > 0]
            if valid_times:
                t = min(valid_times)
            else:
                return target_pos

        # Clamp time to reasonable range
        t = max(0, min(t, 10))

        return target_pos + target_vel * t


class EntityManager:
    """Manages all game entities"""

    def __init__(self):
        self.pigeons: List[Pigeon] = []
        self.projectiles: List[Projectile] = []
        self.next_pigeon_id = 0
        self.spawn_accumulator = 0.0
        self.shot_tracking: Dict[int, int] = {}  # pigeon_id -> shots_fired_at_it
        self.last_dynamic_spawn_check = 0.0  # For dynamic pigeon management

        # Enhanced tracking for AI brain
        self.escaped_targets: List[Dict] = []  # Targets that escaped without engagement
        self.engagement_opportunities: List[Dict] = []  # Missed engagement windows

    def update(self, dt: float, current_time: float, config):
        """Update all entities"""
        # Update pigeons and track range status
        for pigeon in self.pigeons:
            pigeon.update(dt)
            if pigeon.active and not pigeon.is_fleeing:
                pigeon.update_range_status(
                    current_time, config.targeting.MAX_ENGAGEMENT_DISTANCE
                )

        # Update projectiles
        for projectile in self.projectiles:
            projectile.update(dt)

        # Check collisions
        self._check_collisions(current_time, config.targeting.HIT_RADIUS)

        # Track escaped targets and cull out-of-range pigeons
        self._manage_out_of_range_pigeons(current_time, config)

        # Remove inactive entities and fleeing pigeons that are far enough
        self.pigeons = [
            p
            for p in self.pigeons
            if p.active
            and (
                not p.is_fleeing
                or np.linalg.norm(p.position) < config.environment.PIGEON_FLEE_DISTANCE
            )
        ]
        self.projectiles = [
            p
            for p in self.projectiles
            if p.active and np.linalg.norm(p.position) < 2000
        ]

        # Dynamic pigeon management
        self._manage_pigeon_population(current_time, config)

        # Spawn new pigeons (regular spawning)
        self._spawn_pigeons(dt, current_time, config)

    def _check_collisions(self, current_time: float, hit_radius: float):
        """Check projectile-pigeon collisions"""
        for projectile in self.projectiles:
            if not projectile.active or projectile.hit:
                continue

            for pigeon in self.pigeons:
                if not pigeon.active or pigeon.is_fleeing:  # Don't hit fleeing pigeons
                    continue

                distance = np.linalg.norm(projectile.position - pigeon.position)
                if distance < hit_radius + pigeon.size / 2:
                    # Hit!
                    projectile.hit = True
                    projectile.active = False

                    # Make pigeon flee instead of disappearing
                    pigeon.start_fleeing()
                    pigeon.hit_time = current_time

                    log.info(
                        f"Hit pigeon {pigeon.id} at time {current_time:.2f}s - pigeon fleeing!"
                    )

                    # Update targeting algorithm stats if available
                    if hasattr(projectile, "_evaluator") and hasattr(
                        projectile, "_algorithm"
                    ):
                        if (
                            hasattr(projectile, "_target_pigeon_id")
                            and projectile._target_pigeon_id == pigeon.id
                        ):
                            # Update the algorithm's hit count
                            projectile._evaluator.algorithms[
                                projectile._algorithm
                            ].hits += 1
                            projectile._evaluator.session_metrics[
                                "total_pigeons_hit"
                            ] += 1
                            
                            # Notify targeting system of hit for multi-target tracking
                            if hasattr(projectile, "_targeting_system"):
                                projectile._targeting_system.on_hit_confirmed()

    def _manage_out_of_range_pigeons(self, current_time: float, config):
        """Manage pigeons that have been out of range too long"""
        if not config.environment.PIGEON_RELEVANCE_CHECK:
            return

        pigeons_to_remove = []

        for pigeon in self.pigeons:
            if (
                pigeon.active
                and not pigeon.is_fleeing
                and pigeon.is_out_of_range_too_long(
                    current_time,
                    config.environment.PIGEON_OUT_OF_RANGE_TIMEOUT,
                    config.targeting.MAX_ENGAGEMENT_DISTANCE,
                )
            ):
                # Log escaped target for AI brain
                if config.brain.LOG_TARGET_ESCAPES and pigeon.shots_taken_at == 0:
                    escape_data = {
                        "pigeon_id": pigeon.id,
                        "spawn_time": pigeon.spawn_time,
                        "escape_time": current_time,
                        "lifetime": current_time - pigeon.spawn_time,
                        "shots_taken": pigeon.shots_taken_at,
                        "crossed_range": pigeon.crosses_range,
                        "final_distance": np.linalg.norm(pigeon.position),
                    }
                    self.escaped_targets.append(escape_data)
                    log.debug(
                        f"Target escaped: Pigeon {pigeon.id} (lifetime: {escape_data['lifetime']:.1f}s, shots: {pigeon.shots_taken_at})"
                    )

                pigeons_to_remove.append(pigeon)

        # Remove escaped pigeons
        for pigeon in pigeons_to_remove:
            pigeon.active = False

    def _manage_pigeon_population(self, current_time: float, config):
        """Dynamically manage pigeon population to maintain engagement"""
        # Only check every few seconds to avoid constant spawning
        if (
            current_time - self.last_dynamic_spawn_check
            < config.environment.DYNAMIC_SPAWN_COOLDOWN
        ):
            return

        self.last_dynamic_spawn_check = current_time

        # Count pigeons within engagement range
        pigeons_in_range = []
        pigeons_out_of_range = []

        for pigeon in self.pigeons:
            if not pigeon.active or pigeon.is_fleeing:
                continue

            distance = np.linalg.norm(pigeon.position)
            if distance <= config.targeting.MAX_ENGAGEMENT_DISTANCE:
                pigeons_in_range.append(pigeon)
            elif distance > config.environment.PIGEON_CULL_DISTANCE:
                pigeons_out_of_range.append(pigeon)

        in_range_count = len(pigeons_in_range)

        # If too few pigeons in range, spawn more and cull distant ones
        if in_range_count < config.environment.MIN_PIGEONS_IN_RANGE:
            needed = config.environment.OPTIMAL_PIGEONS_IN_RANGE - in_range_count

            # Cull distant pigeons first to make room
            pigeons_to_cull = min(needed, len(pigeons_out_of_range))
            for i in range(pigeons_to_cull):
                pigeons_out_of_range[i].active = False
                log.debug(f"Culled distant pigeon {pigeons_out_of_range[i].id}")

            # Spawn new pigeons in range with relevance check
            for _ in range(needed):
                if (
                    len(self.pigeons) < config.environment.MAX_PIGEONS * 2
                ):  # Allow more total pigeons
                    pigeon = Pigeon.spawn_random(
                        self.next_pigeon_id,
                        current_time,
                        distance_range=(
                            300,
                            config.targeting.MAX_ENGAGEMENT_DISTANCE * 0.8,
                        ),  # Spawn within range
                        speed_range=(
                            config.environment.PIGEON_SPEED_MIN,
                            config.environment.PIGEON_SPEED_MAX,
                        ),
                        ensure_relevance=config.environment.PIGEON_RELEVANCE_CHECK,
                    )
                    self.pigeons.append(pigeon)
                    self.next_pigeon_id += 1
                    log.debug(
                        f"Dynamically spawned pigeon {pigeon.id} at distance {np.linalg.norm(pigeon.position):.0f} (relevant: {pigeon.crosses_range})"
                    )

            if needed > 0:
                log.info(
                    f"Dynamic spawn: Added {needed} pigeons (was {in_range_count}, now targeting {config.environment.OPTIMAL_PIGEONS_IN_RANGE} in range)"
                )

    def _spawn_pigeons(self, dt: float, current_time: float, config):
        """Spawn new pigeons based on spawn rate"""
        self.spawn_accumulator += dt

        if len(self.pigeons) < config.environment.MAX_PIGEONS:
            spawn_interval = 1.0 / config.environment.PIGEON_SPAWN_RATE
            while self.spawn_accumulator >= spawn_interval:
                self.spawn_accumulator -= spawn_interval

                pigeon = Pigeon.spawn_random(
                    self.next_pigeon_id,
                    current_time,
                    speed_range=(
                        config.environment.PIGEON_SPEED_MIN,
                        config.environment.PIGEON_SPEED_MAX,
                    ),
                    ensure_relevance=config.environment.PIGEON_RELEVANCE_CHECK,
                )
                self.pigeons.append(pigeon)
                self.next_pigeon_id += 1
                log.debug(
                    f"Spawned pigeon {pigeon.id} at {pigeon.position} (crosses_range: {pigeon.crosses_range})"
                )

    def fire_projectile(
        self,
        current_time: float,
        target_pigeon: Optional[Pigeon],
        config,
        mode: str = "predictive",
        predicted_position: Optional[np.ndarray] = None,
    ) -> Optional[Projectile]:
        """Fire a projectile at target"""
        if not target_pigeon:
            return None

        # Use predicted position if provided (from targeting system)
        if predicted_position is not None:
            projectile = Projectile.fire_at_position(
                current_time, predicted_position, config.targeting.PROJECTILE_SPEED
            )
        elif mode == "predictive":
            projectile = Projectile.fire_with_prediction(
                current_time, target_pigeon, config.targeting.PROJECTILE_SPEED
            )
        else:
            projectile = Projectile.fire_at_position(
                current_time, target_pigeon.position, config.targeting.PROJECTILE_SPEED
            )

        projectile.target_id = target_pigeon.id
        self.projectiles.append(projectile)
        return projectile

    def get_visible_pigeons(self, camera) -> List[Pigeon]:
        """Get pigeons visible in current camera view"""
        visible = []
        for pigeon in self.pigeons:
            if pigeon.active:
                yaw, pitch, _ = pigeon.to_spherical()
                if camera.angles_to_screen(yaw, pitch) is not None:
                    visible.append(pigeon)
        return visible

    def record_shot_at_pigeon(self, pigeon_id: int):
        """Record that a shot was fired at a specific pigeon"""
        if pigeon_id not in self.shot_tracking:
            self.shot_tracking[pigeon_id] = 0
        self.shot_tracking[pigeon_id] += 1

        # Update the pigeon's shot counter
        for pigeon in self.pigeons:
            if pigeon.id == pigeon_id:
                pigeon.shots_taken_at += 1
                break

    def get_shots_at_pigeon(self, pigeon_id: int) -> int:
        """Get number of shots fired at a specific pigeon"""
        return self.shot_tracking.get(pigeon_id, 0)

    def get_brain_metrics(self) -> Dict[str, Any]:
        """Get enhanced metrics for AI brain analysis"""
        return {
            "escaped_targets": len(self.escaped_targets),
            "recent_escapes": [
                e for e in self.escaped_targets if time.time() - e["escape_time"] < 60
            ],
            "engagement_opportunities": len(self.engagement_opportunities),
            "active_pigeons": len(
                [p for p in self.pigeons if p.active and not p.is_fleeing]
            ),
            "fleeing_pigeons": len([p for p in self.pigeons if p.is_fleeing]),
            "pigeons_with_shots": len(
                [p for p in self.pigeons if p.shots_taken_at > 0]
            ),
            "relevant_spawns": len([p for p in self.pigeons if p.crosses_range]),
        }
