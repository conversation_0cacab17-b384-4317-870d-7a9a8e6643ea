"""Test run the turret simulation for a fixed duration"""

import subprocess
import time
import os


def run_simulation(duration=30):
    """Run the simulation for a specified duration"""
    print(f"Starting turret simulation for {duration} seconds...")

    # Start the simulation
    proc = subprocess.Popen(["uv", "run", "main.py"])

    try:
        # Wait for the specified duration
        time.sleep(duration)

        print(f"\nSimulation ran for {duration} seconds. Stopping...")

        # Send terminate signal
        proc.terminate()

        # Wait for process to end gracefully
        try:
            proc.wait(timeout=5)
        except subprocess.TimeoutExpired:
            print("Process didn't terminate gracefully, forcing...")
            proc.kill()
            proc.wait()

    except KeyboardInterrupt:
        print("\nInterrupted by user. Stopping simulation...")
        proc.terminate()
        proc.wait()

    print("\nSimulation stopped.")

    # Check if output files were created
    if os.path.exists("turret_metrics.json"):
        print("\nMetrics file created. Reading summary...")
        with open("turret_metrics.json", "r") as f:
            import json

            data = json.load(f)
            print(f"Total pigeons spawned: {data['session']['total_pigeons_spawned']}")
            print(f"Total pigeons hit: {data['session']['total_pigeons_hit']}")
            print(f"Total shots fired: {data['session']['total_shots_fired']}")
            print(f"Session duration: {data['session']['session_duration']:.1f}s")

            if data["algorithms"]:
                print("\nAlgorithm performance:")
                for algo, stats in data["algorithms"].items():
                    print(
                        f"  {algo}: {stats['accuracy']:.1f}% accuracy ({stats['hits']}/{stats['shots_fired']} hits)"
                    )


if __name__ == "__main__":
    # Run for 20 seconds
    run_simulation(20)
