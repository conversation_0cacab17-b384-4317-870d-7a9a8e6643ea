"""Meta-Brain: High-Level Experiment Orchestration and Strategic Optimization

The Meta-Brain operates at a higher level than the individual AI brains, orchestrating
parallel experiments and reasoning about long-term optimization strategies.

Primary KPI: Unique pigeons hit per minute
"""

import multiprocessing as mp
import threading
import time
import json
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Any, Tu<PERSON>, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import numpy as np
from loguru import logger as log

try:
    import openai

    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    log.warning("OpenAI not available for meta-brain reasoning")

from config import CONFIG


@dataclass
class ExperimentPlan:
    """Plan for a specific experiment"""

    id: str
    strategy_type: str  # "parameter_sweep", "algorithm_test", "hybrid", etc.
    parameters: Dict[str, Any]
    expected_duration: float  # seconds
    priority: float  # 0.0 to 1.0
    reasoning: str
    created_at: str
    dependencies: List[str] = None  # Other experiment IDs this depends on


@dataclass
class ExperimentResult:
    """Comprehensive result from an experiment"""

    plan_id: str
    start_time: str
    end_time: str
    duration: float

    # Core KPIs
    unique_pigeons_hit: int
    unique_pigeons_hit_per_minute: float
    total_shots_fired: int
    accuracy: float

    # Detailed metrics
    pigeons_spawned: int
    pigeons_escaped: int
    average_reaction_time: float
    parameter_stability: float

    # Meta information
    cpu_usage: float
    memory_usage: float
    error_count: int
    warnings: List[str]

    # Raw data path for detailed analysis
    raw_data_path: str


@dataclass
class FeatureRequest:
    """Structured Product Requirements Document for new features"""

    id: str
    title: str
    priority: str  # "Critical", "High", "Medium", "Low"
    category: str  # "Algorithm", "UI", "Performance", "Analytics", etc.

    # Problem statement
    problem_description: str
    current_limitations: str
    impact_on_kpi: str

    # Solution proposal
    proposed_solution: str
    technical_requirements: List[str]
    estimated_complexity: str  # "Low", "Medium", "High", "Very High"

    # Evidence
    supporting_data: Dict[str, Any]
    experiment_evidence: List[str]  # Experiment IDs that support this request

    # Metadata
    created_at: str
    status: str  # "Open", "In Progress", "Completed", "Rejected"


class ExperimentOrchestrator:
    """Manages parallel execution of experiments"""

    def __init__(self, max_parallel: int = 4):
        self.max_parallel = max_parallel
        self.active_experiments: Dict[str, mp.Process] = {}
        self.experiment_queue: List[ExperimentPlan] = []
        self.completed_experiments: List[ExperimentResult] = []
        self.results_dir = Path("meta_brain_results")
        self.results_dir.mkdir(exist_ok=True)

    def add_experiment(self, plan: ExperimentPlan):
        """Add an experiment to the queue"""
        self.experiment_queue.append(plan)
        self.experiment_queue.sort(key=lambda x: x.priority, reverse=True)
        log.info(f"Added experiment {plan.id} to queue (priority: {plan.priority})")

    def start_next_experiment(self) -> bool:
        """Start the next experiment if resources are available"""
        if len(self.active_experiments) >= self.max_parallel:
            return False

        if not self.experiment_queue:
            return False

        plan = self.experiment_queue.pop(0)

        # Check dependencies
        if plan.dependencies:
            completed_ids = {r.plan_id for r in self.completed_experiments}
            if not all(dep_id in completed_ids for dep_id in plan.dependencies):
                # Re-queue for later
                self.experiment_queue.append(plan)
                return False

        # Start the experiment
        process = mp.Process(target=self._run_experiment, args=(plan,))
        process.start()
        self.active_experiments[plan.id] = process

        log.info(f"Started experiment {plan.id} ({plan.strategy_type})")
        return True

    def _run_experiment(self, plan: ExperimentPlan):
        """Run a single experiment in a separate process"""
        try:
            # Import here to avoid issues with multiprocessing
            from headless_simulator import HeadlessSimulator

            # Create unique output directory
            output_dir = self.results_dir / plan.id
            output_dir.mkdir(exist_ok=True)

            # Configure the experiment
            config_overrides = plan.parameters.copy()

            # Run the headless simulator
            simulator = HeadlessSimulator(
                duration=plan.expected_duration,
                config_overrides=config_overrides,
                output_dir=str(output_dir),
            )

            result = simulator.run()

            # Save detailed results
            result_file = output_dir / "result.json"
            with open(result_file, "w") as f:
                json.dump(asdict(result), f, indent=2)

            log.info(
                f"Experiment {plan.id} completed: {result.unique_pigeons_hit_per_minute:.2f} PHPM"
            )

        except Exception as e:
            log.error(f"Experiment {plan.id} failed: {e}")
            # Create error result
            error_result = ExperimentResult(
                plan_id=plan.id,
                start_time=datetime.now().isoformat(),
                end_time=datetime.now().isoformat(),
                duration=0.0,
                unique_pigeons_hit=0,
                unique_pigeons_hit_per_minute=0.0,
                total_shots_fired=0,
                accuracy=0.0,
                pigeons_spawned=0,
                pigeons_escaped=0,
                average_reaction_time=0.0,
                parameter_stability=0.0,
                cpu_usage=0.0,
                memory_usage=0.0,
                error_count=1,
                warnings=[str(e)],
                raw_data_path="",
            )

            error_file = self.results_dir / plan.id / "error_result.json"
            error_file.parent.mkdir(exist_ok=True)
            with open(error_file, "w") as f:
                json.dump(asdict(error_result), f, indent=2)

    def check_completed_experiments(self):
        """Check for completed experiments and collect results"""
        completed_ids = []

        for exp_id, process in self.active_experiments.items():
            if not process.is_alive():
                completed_ids.append(exp_id)

                # Load the result
                result_file = self.results_dir / exp_id / "result.json"
                if result_file.exists():
                    with open(result_file, "r") as f:
                        result_data = json.load(f)
                    result = ExperimentResult(**result_data)
                    self.completed_experiments.append(result)
                    log.info(f"Collected result for experiment {exp_id}")
                else:
                    log.warning(f"No result file found for experiment {exp_id}")

        # Clean up completed processes
        for exp_id in completed_ids:
            del self.active_experiments[exp_id]

    def get_status(self) -> Dict[str, Any]:
        """Get current orchestrator status"""
        return {
            "active_experiments": len(self.active_experiments),
            "queued_experiments": len(self.experiment_queue),
            "completed_experiments": len(self.completed_experiments),
            "max_parallel": self.max_parallel,
            "active_experiment_ids": list(self.active_experiments.keys()),
            "next_queued": self.experiment_queue[0].id
            if self.experiment_queue
            else None,
        }


class StrategyPlanner:
    """Plans and prioritizes experiments based on meta-learning"""

    def __init__(self):
        self.strategy_history: List[ExperimentResult] = []
        self.feature_requests: List[FeatureRequest] = []
        self.load_history()

    def load_history(self):
        """Load previous experiment history"""
        history_file = Path("meta_brain_history.json")
        if history_file.exists():
            try:
                with open(history_file, "r") as f:
                    data = json.load(f)
                self.strategy_history = [
                    ExperimentResult(**r) for r in data.get("experiments", [])
                ]
                self.feature_requests = [
                    FeatureRequest(**r) for r in data.get("feature_requests", [])
                ]
                log.info(f"Loaded {len(self.strategy_history)} historical experiments")
            except Exception as e:
                log.error(f"Failed to load history: {e}")

    def save_history(self):
        """Save experiment history and feature requests"""
        history_file = Path("meta_brain_history.json")
        data = {
            "experiments": [asdict(r) for r in self.strategy_history],
            "feature_requests": [asdict(r) for r in self.feature_requests],
            "last_updated": datetime.now().isoformat(),
        }
        with open(history_file, "w") as f:
            json.dump(data, f, indent=2)

    def generate_experiment_plans(self, count: int = 5) -> List[ExperimentPlan]:
        """Generate a set of experiment plans based on current knowledge"""
        plans = []

        # Strategy 1: Parameter sweep around best known configurations
        if self.strategy_history:
            best_result = max(
                self.strategy_history, key=lambda x: x.unique_pigeons_hit_per_minute
            )
            plans.extend(self._generate_parameter_sweep_plans(best_result, count=2))

        # Strategy 2: Explore underexplored parameter regions
        plans.extend(self._generate_exploration_plans(count=2))

        # Strategy 3: Test specific hypotheses
        plans.extend(self._generate_hypothesis_plans(count=1))

        # Prioritize plans
        for i, plan in enumerate(plans):
            plan.priority = self._calculate_priority(plan)

        return sorted(plans, key=lambda x: x.priority, reverse=True)[:count]

    def _generate_parameter_sweep_plans(
        self, best_result: ExperimentResult, count: int
    ) -> List[ExperimentPlan]:
        """Generate plans that sweep around the best known configuration"""
        plans = []

        # Load the best configuration
        best_config_file = (
            Path("meta_brain_results") / best_result.plan_id / "config.json"
        )
        if not best_config_file.exists():
            return plans

        try:
            with open(best_config_file, "r") as f:
                best_config = json.load(f)
        except Exception as e:
            log.error(f"Failed to load best config: {e}")
            return plans

        # Generate variations
        for i in range(count):
            plan_id = f"param_sweep_{uuid.uuid4().hex[:8]}"

            # Create parameter variations (±10% from best)
            varied_params = best_config.copy()
            for key in [
                "fire_cooldown",
                "angle_threshold_close",
                "angle_threshold_far",
            ]:
                if key in varied_params:
                    base_value = varied_params[key]
                    variation = np.random.uniform(0.9, 1.1)
                    varied_params[key] = base_value * variation

            plan = ExperimentPlan(
                id=plan_id,
                strategy_type="parameter_sweep",
                parameters=varied_params,
                expected_duration=120.0,  # 2 minutes
                priority=0.8,
                reasoning=f"Parameter sweep around best configuration (PHPM: {best_result.unique_pigeons_hit_per_minute:.2f})",
                created_at=datetime.now().isoformat(),
            )
            plans.append(plan)

        return plans

    def _generate_exploration_plans(self, count: int) -> List[ExperimentPlan]:
        """Generate plans to explore underexplored regions"""
        plans = []

        for i in range(count):
            plan_id = f"exploration_{uuid.uuid4().hex[:8]}"

            # Generate random but reasonable parameters
            params = {
                "fire_cooldown": np.random.uniform(0.1, 0.8),
                "fire_cooldown_close": np.random.uniform(0.1, 0.5),
                "angle_threshold_close": np.random.uniform(1.0, 5.0),
                "angle_threshold_far": np.random.uniform(5.0, 12.0),
                "max_engagement_distance": np.random.uniform(800, 1800),
                "max_shots_per_target": np.random.randint(1, 6),
            }

            plan = ExperimentPlan(
                id=plan_id,
                strategy_type="exploration",
                parameters=params,
                expected_duration=90.0,
                priority=0.6,
                reasoning="Explore underexplored parameter regions for potential breakthroughs",
                created_at=datetime.now().isoformat(),
            )
            plans.append(plan)

        return plans

    def _generate_hypothesis_plans(self, count: int) -> List[ExperimentPlan]:
        """Generate plans to test specific hypotheses"""
        plans = []

        # Hypothesis 1: Very fast fire rate with high precision
        plan_id = f"hypothesis_{uuid.uuid4().hex[:8]}"
        params = {
            "fire_cooldown": 0.1,
            "fire_cooldown_close": 0.05,
            "angle_threshold_close": 1.0,
            "angle_threshold_far": 2.0,
            "max_engagement_distance": 1000,
            "max_shots_per_target": 1,
        }

        plan = ExperimentPlan(
            id=plan_id,
            strategy_type="hypothesis",
            parameters=params,
            expected_duration=150.0,
            priority=0.7,
            reasoning="Test hypothesis: Very fast fire rate with high precision targeting",
            created_at=datetime.now().isoformat(),
        )
        plans.append(plan)

        # Hypothesis 2: Custom algorithm testing
        if OPENAI_AVAILABLE and count > 1:
            custom_plan = self._generate_custom_algorithm_plan()
            if custom_plan:
                plans.append(custom_plan)

        return plans[:count]

    def _generate_custom_algorithm_plan(self) -> Optional[ExperimentPlan]:
        """Generate a plan to test a custom algorithm"""
        try:
            # Generate a custom algorithm using AI
            algorithm_code = self._generate_custom_algorithm()
            if not algorithm_code:
                return None

            plan_id = f"custom_algo_{uuid.uuid4().hex[:8]}"

            # Save the algorithm code
            algo_file = (
                Path("meta_brain_results") / "custom_algorithms" / f"{plan_id}.py"
            )
            algo_file.parent.mkdir(exist_ok=True)
            with open(algo_file, "w") as f:
                f.write(algorithm_code)

            # Create experiment plan with custom algorithm
            params = {
                "custom_algorithm": str(algo_file),
                "fire_cooldown": 0.3,
                "angle_threshold_close": 2.5,
                "angle_threshold_far": 7.0,
                "max_engagement_distance": 1200,
            }

            plan = ExperimentPlan(
                id=plan_id,
                strategy_type="custom_algorithm",
                parameters=params,
                expected_duration=120.0,
                priority=0.9,  # High priority for custom algorithms
                reasoning="Test AI-generated custom targeting algorithm",
                created_at=datetime.now().isoformat(),
            )

            log.info(f"Generated custom algorithm plan: {plan_id}")
            return plan

        except Exception as e:
            log.error(f"Failed to generate custom algorithm plan: {e}")
            return None

    def _generate_custom_algorithm(self) -> Optional[str]:
        """Generate a custom targeting algorithm using AI"""
        if not OPENAI_AVAILABLE:
            return None

        try:
            # Analyze recent performance to inform algorithm design
            recent_results = (
                self.strategy_history[-5:]
                if len(self.strategy_history) >= 5
                else self.strategy_history
            )

            performance_summary = ""
            if recent_results:
                avg_accuracy = np.mean([r.accuracy for r in recent_results])
                avg_phpm = np.mean(
                    [r.unique_pigeons_hit_per_minute for r in recent_results]
                )
                performance_summary = f"Recent performance: {avg_accuracy:.1f}% accuracy, {avg_phpm:.1f} PHPM. "

            prompt = f"""Generate a custom targeting algorithm for a pigeon turret simulator.

{performance_summary}

The algorithm should:
1. Select the best pigeon to target from available pigeons
2. Calculate where to aim (considering pigeon movement)
3. Return target_id, target_yaw, target_pitch, and confidence

Available data for each pigeon:
- id, position (3D array), velocity (3D array), distance, yaw, pitch, speed, shots_taken, is_fleeing

Available helper functions:
- calculate_angle_diff(yaw1, pitch1, yaw2, pitch2)
- calculate_movement_time(yaw, pitch)
- calculate_lead_time(distance, speed)

Constants:
- MAX_ENGAGEMENT_DISTANCE: 1200
- PROJECTILE_SPEED: 500

Write a complete select_target() function that implements a novel targeting strategy.
Focus on maximizing unique pigeons hit per minute.
Be creative but practical. The code must be executable Python.
"""

            # This is a placeholder - in real implementation, would call OpenAI
            # For now, return a template with some variations
            template = self._get_algorithm_template_variation()
            return template

        except Exception as e:
            log.error(f"Failed to generate custom algorithm: {e}")
            return None

    def _get_algorithm_template_variation(self) -> str:
        """Get a variation of the algorithm template"""
        import random

        # Different scoring strategies
        strategies = [
            # Strategy 1: Aggressive close-range focus
            """
def select_target():
    if not pigeons:
        return None
    
    best_score = -float('inf')
    best_target = None
    
    for pigeon in sorted(pigeons, key=lambda p: p['distance']):
        if pigeon['is_fleeing'] or pigeon['distance'] > 800:  # Shorter range
            continue
        
        # Heavily prioritize very close targets
        if pigeon['distance'] < 300:
            distance_score = 10.0
        else:
            distance_score = 1.0 / (1.0 + pigeon['distance'] / 200)
        
        # Prefer stationary or slow targets
        speed_score = 1.0 / (1.0 + pigeon['speed'] / 50)
        
        # Minimal angle consideration for close targets
        angle_diff = calculate_angle_diff(
            camera['yaw'], camera['pitch'],
            pigeon['yaw'], pigeon['pitch']
        )
        angle_score = 1.0 / (1.0 + angle_diff / 45)
        
        score = distance_score * 5.0 + speed_score * 2.0 + angle_score * 0.5
        
        if score > best_score:
            best_score = score
            
            # Minimal lead for close targets
            if pigeon['distance'] < 400:
                lead_multiplier = 0.3
            else:
                lead_multiplier = 0.6
                
            lead_time = calculate_lead_time(pigeon['distance'], pigeon['speed']) * lead_multiplier
            future_pos = [
                pigeon['position'][0] + pigeon['velocity'][0] * lead_time,
                pigeon['position'][1] + pigeon['velocity'][1] * lead_time,
                pigeon['position'][2] + pigeon['velocity'][2] * lead_time
            ]
            
            dist = max(np.linalg.norm(future_pos), 0.001)
            future_yaw = math.degrees(math.atan2(future_pos[0], future_pos[2])) % 360
            future_pitch = math.degrees(math.asin(np.clip(future_pos[1] / dist, -1, 1)))
            
            best_target = {
                'target_id': pigeon['id'],
                'target_yaw': future_yaw,
                'target_pitch': future_pitch,
                'confidence': min(score / 10.0, 1.0)
            }
    
    return best_target
""",
            # Strategy 2: Predictive interception focus
            """
def select_target():
    if not pigeons:
        return None
    
    best_score = -float('inf')
    best_target = None
    
    for pigeon in pigeons:
        if pigeon['is_fleeing'] or pigeon['distance'] > MAX_ENGAGEMENT_DISTANCE:
            continue
        
        # Calculate interception point
        movement_time = calculate_movement_time(pigeon['yaw'], pigeon['pitch'])
        projectile_time = pigeon['distance'] / PROJECTILE_SPEED
        total_time = movement_time + projectile_time
        
        # Predict future position
        future_pos = [
            pigeon['position'][0] + pigeon['velocity'][0] * total_time,
            pigeon['position'][1] + pigeon['velocity'][1] * total_time,
            pigeon['position'][2] + pigeon['velocity'][2] * total_time
        ]
        
        future_dist = np.linalg.norm(future_pos)
        
        # Skip if target will be out of range
        if future_dist > MAX_ENGAGEMENT_DISTANCE * 1.2:
            continue
        
        # Score based on interception feasibility
        distance_score = 1.0 / (1.0 + future_dist / 600)
        
        # Prefer predictable targets (consistent velocity)
        speed_consistency = 1.0 / (1.0 + abs(pigeon['speed'] - 100) / 50)
        
        # Time to intercept score
        time_score = 1.0 / (1.0 + total_time / 2.0)
        
        # Fresh target bonus
        freshness_score = 1.0 / (1.0 + pigeon['shots_taken'] * 0.5)
        
        score = distance_score * 2.0 + speed_consistency * 1.5 + time_score * 1.0 + freshness_score * 1.5
        
        if score > best_score:
            best_score = score
            
            dist = max(future_dist, 0.001)
            future_yaw = math.degrees(math.atan2(future_pos[0], future_pos[2])) % 360
            future_pitch = math.degrees(math.asin(np.clip(future_pos[1] / dist, -1, 1)))
            
            best_target = {
                'target_id': pigeon['id'],
                'target_yaw': future_yaw,
                'target_pitch': future_pitch,
                'confidence': min(score / 5.0, 1.0)
            }
    
    return best_target
""",
        ]

        return random.choice(strategies)

    def _calculate_priority(self, plan: ExperimentPlan) -> float:
        """Calculate priority score for an experiment plan"""
        base_priority = plan.priority

        # Boost priority for strategies that have worked well historically
        if self.strategy_history:
            strategy_results = [
                r
                for r in self.strategy_history
                if r.plan_id.startswith(plan.strategy_type)
            ]
            if strategy_results:
                avg_performance = np.mean(
                    [r.unique_pigeons_hit_per_minute for r in strategy_results]
                )
                if avg_performance > 5.0:  # Above average performance
                    base_priority += 0.1

        # Reduce priority for recently tested similar configurations
        # (Implementation would compare parameter similarity)

        return min(1.0, base_priority)

    def analyze_results(self, results: List[ExperimentResult]):
        """Analyze experiment results and update strategy"""
        self.strategy_history.extend(results)

        # Generate insights
        insights = self._generate_insights()

        # Check if we should generate feature requests
        self._check_for_feature_needs(results)

        # Save updated history
        self.save_history()

        return insights

    def _generate_insights(self) -> List[str]:
        """Generate insights from experiment history"""
        insights = []

        if len(self.strategy_history) < 3:
            return ["Need more experiments to generate meaningful insights"]

        # Analyze performance trends
        recent_results = sorted(self.strategy_history, key=lambda x: x.start_time)[-10:]
        performances = [r.unique_pigeons_hit_per_minute for r in recent_results]

        if len(performances) >= 5:
            trend = np.polyfit(range(len(performances)), performances, 1)[0]
            if trend > 0.1:
                insights.append(
                    "Performance is trending upward - current strategy is working"
                )
            elif trend < -0.1:
                insights.append("Performance is declining - need to adjust strategy")
            else:
                insights.append(
                    "Performance is stable - consider more aggressive exploration"
                )

        # Analyze best strategies
        strategy_performance = {}
        for result in self.strategy_history:
            strategy = result.plan_id.split("_")[0]
            if strategy not in strategy_performance:
                strategy_performance[strategy] = []
            strategy_performance[strategy].append(result.unique_pigeons_hit_per_minute)

        for strategy, performances in strategy_performance.items():
            avg_perf = np.mean(performances)
            insights.append(
                f"{strategy} strategy average: {avg_perf:.2f} PHPM ({len(performances)} experiments)"
            )

        return insights

    def _check_for_feature_needs(self, results: List[ExperimentResult]):
        """Check if we need to request new features based on results"""
        # Example: If accuracy is consistently low, request better targeting algorithms
        recent_accuracies = [r.accuracy for r in results[-5:]]
        if recent_accuracies and np.mean(recent_accuracies) < 0.3:
            self._create_feature_request(
                title="Advanced Targeting Algorithm",
                priority="High",
                category="Algorithm",
                problem_description="Current targeting accuracy is consistently below 30%",
                proposed_solution="Implement machine learning-based predictive targeting",
                supporting_data={"recent_accuracies": recent_accuracies},
            )

    def _create_feature_request(
        self,
        title: str,
        priority: str,
        category: str,
        problem_description: str,
        proposed_solution: str,
        supporting_data: Dict[str, Any],
    ):
        """Create a new feature request"""
        # Check if similar request already exists
        existing = [
            fr
            for fr in self.feature_requests
            if fr.title == title and fr.status in ["Open", "In Progress"]
        ]
        if existing:
            return

        feature_request = FeatureRequest(
            id=f"fr_{uuid.uuid4().hex[:8]}",
            title=title,
            priority=priority,
            category=category,
            problem_description=problem_description,
            current_limitations="Limited by current implementation",
            impact_on_kpi="Could significantly improve unique pigeons hit per minute",
            proposed_solution=proposed_solution,
            technical_requirements=["To be determined"],
            estimated_complexity="Medium",
            supporting_data=supporting_data,
            experiment_evidence=[r.plan_id for r in self.strategy_history[-3:]],
            created_at=datetime.now().isoformat(),
            status="Open",
        )

        self.feature_requests.append(feature_request)
        log.info(f"Created feature request: {title}")


class MetaBrain:
    """Main Meta-Brain orchestrator"""

    def __init__(self, max_parallel_experiments: int = 4):
        self.orchestrator = ExperimentOrchestrator(max_parallel_experiments)
        self.strategy_planner = StrategyPlanner()
        self.running = False
        self.start_time = None

        # Performance tracking
        self.performance_history: List[Tuple[datetime, float]] = []
        self.best_phpm = 0.0

        log.info(
            f"Meta-Brain initialized with {max_parallel_experiments} parallel experiment slots"
        )

    def start(self):
        """Start the meta-brain orchestration"""
        if self.running:
            log.warning("Meta-Brain is already running")
            return

        self.running = True
        self.start_time = datetime.now()

        log.info("🧠 Meta-Brain starting orchestration...")

        # Start the main loop in a separate thread
        self.main_thread = threading.Thread(target=self._main_loop, daemon=True)
        self.main_thread.start()

        log.info("Meta-Brain orchestration started")

    def stop(self):
        """Stop the meta-brain orchestration"""
        self.running = False
        if hasattr(self, "main_thread"):
            self.main_thread.join(timeout=5.0)
        log.info("Meta-Brain orchestration stopped")

    def _main_loop(self):
        """Main orchestration loop"""
        last_planning_time = 0
        planning_interval = 300  # Plan new experiments every 5 minutes

        while self.running:
            try:
                current_time = time.time()

                # Check for completed experiments
                self.orchestrator.check_completed_experiments()

                # Start new experiments if slots are available
                while self.orchestrator.start_next_experiment():
                    pass

                # Plan new experiments periodically
                if current_time - last_planning_time > planning_interval:
                    self._plan_new_experiments()
                    last_planning_time = current_time

                # Update performance tracking
                self._update_performance_tracking()

                # Log status periodically
                if int(current_time) % 60 == 0:  # Every minute
                    self._log_status()

                time.sleep(10)  # Check every 10 seconds

            except Exception as e:
                log.error(f"Error in meta-brain main loop: {e}")
                time.sleep(30)  # Wait before retrying

    def _plan_new_experiments(self):
        """Plan and queue new experiments"""
        try:
            # Analyze recent results
            recent_results = self.orchestrator.completed_experiments[-10:]
            if recent_results:
                insights = self.strategy_planner.analyze_results(recent_results)
                log.info(f"Generated {len(insights)} insights from recent experiments")
                for insight in insights:
                    log.info(f"💡 {insight}")

            # Generate new experiment plans
            new_plans = self.strategy_planner.generate_experiment_plans(count=8)

            for plan in new_plans:
                self.orchestrator.add_experiment(plan)

            log.info(f"Planned {len(new_plans)} new experiments")

        except Exception as e:
            log.error(f"Error planning new experiments: {e}")

    def _update_performance_tracking(self):
        """Update long-term performance tracking"""
        if not self.orchestrator.completed_experiments:
            return

        # Calculate current best performance
        recent_results = self.orchestrator.completed_experiments[-5:]
        if recent_results:
            current_best = max(r.unique_pigeons_hit_per_minute for r in recent_results)

            if current_best > self.best_phpm:
                self.best_phpm = current_best
                log.info(f"🎯 New best performance: {self.best_phpm:.2f} PHPM!")

            # Track performance over time
            self.performance_history.append((datetime.now(), current_best))

            # Keep only last 24 hours of data
            cutoff = datetime.now() - timedelta(hours=24)
            self.performance_history = [
                (t, p) for t, p in self.performance_history if t > cutoff
            ]

    def _log_status(self):
        """Log current meta-brain status"""
        status = self.orchestrator.get_status()
        uptime = datetime.now() - self.start_time if self.start_time else timedelta(0)

        log.info(
            f"🧠 Meta-Brain Status: {status['active_experiments']} active, "
            f"{status['queued_experiments']} queued, "
            f"{status['completed_experiments']} completed | "
            f"Best PHPM: {self.best_phpm:.2f} | "
            f"Uptime: {uptime}"
        )

    def get_comprehensive_status(self) -> Dict[str, Any]:
        """Get comprehensive status including analytics"""
        status = self.orchestrator.get_status()

        # Add performance analytics
        if self.performance_history:
            recent_performance = [p for t, p in self.performance_history[-10:]]
            status.update(
                {
                    "best_phpm": self.best_phpm,
                    "recent_avg_phpm": np.mean(recent_performance)
                    if recent_performance
                    else 0.0,
                    "performance_trend": "improving"
                    if len(recent_performance) >= 2
                    and recent_performance[-1] > recent_performance[0]
                    else "stable",
                    "total_runtime_hours": (
                        datetime.now() - self.start_time
                    ).total_seconds()
                    / 3600
                    if self.start_time
                    else 0,
                }
            )

        # Add feature requests summary
        open_requests = [
            fr for fr in self.strategy_planner.feature_requests if fr.status == "Open"
        ]
        status.update(
            {
                "open_feature_requests": len(open_requests),
                "high_priority_requests": len(
                    [fr for fr in open_requests if fr.priority == "High"]
                ),
            }
        )

        return status

    def generate_feature_requests_report(self) -> str:
        """Generate a comprehensive feature requests report"""
        open_requests = [
            fr for fr in self.strategy_planner.feature_requests if fr.status == "Open"
        ]

        if not open_requests:
            return "No open feature requests at this time."

        report = "# Meta-Brain Feature Requests Report\n\n"

        for priority in ["Critical", "High", "Medium", "Low"]:
            priority_requests = [fr for fr in open_requests if fr.priority == priority]
            if not priority_requests:
                continue

            report += f"## {priority} Priority\n\n"

            for fr in priority_requests:
                report += f"### {fr.title}\n"
                report += f"**Category:** {fr.category}\n"
                report += f"**Problem:** {fr.problem_description}\n"
                report += f"**Proposed Solution:** {fr.proposed_solution}\n"
                report += f"**Estimated Complexity:** {fr.estimated_complexity}\n"
                report += f"**Supporting Evidence:** {len(fr.experiment_evidence)} experiments\n\n"

        return report


def main():
    """Main entry point for meta-brain"""
    # Configure logging
    log.remove()
    log.add(
        "meta_brain.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
        level="INFO",
        rotation="1 day",
    )
    log.add(
        lambda msg: print(msg, end=""),
        format="{time:HH:mm:ss} | {level} | {message}\n",
        level="INFO",
        colorize=True,
    )

    # Create and start meta-brain
    meta_brain = MetaBrain(max_parallel_experiments=4)

    try:
        meta_brain.start()

        # Keep running until interrupted
        while True:
            time.sleep(60)

            # Print status every minute
            status = meta_brain.get_comprehensive_status()
            print(f"\n🧠 Meta-Brain Status: {status}")

    except KeyboardInterrupt:
        log.info("Shutting down meta-brain...")
        meta_brain.stop()

        # Generate final report
        report = meta_brain.generate_feature_requests_report()
        print("\n" + "=" * 50)
        print("FINAL FEATURE REQUESTS REPORT")
        print("=" * 50)
        print(report)


if __name__ == "__main__":
    main()
