"""3D Environment with 360-degree panoramic background"""

import pygame
import numpy as np
import math
from typing import Tuple, List
from PIL import Image
from loguru import logger as log
import os

from camera import Camera
from entities import Pigeon, Projectile, EntityManager


class PanoramaRenderer:
    """Renders 360-degree panoramic background"""

    def __init__(self, window_size: Tuple[int, int]):
        self.window_width, self.window_height = window_size
        self.panorama_surface = None
        self.default_background = None
        self._create_default_background()

    def _create_default_background(self):
        """Create a default gradient background if no panorama is available"""
        self.default_background = pygame.Surface(
            (self.window_width, self.window_height)
        )

        # Create sky gradient
        for y in range(self.window_height):
            # Sky color gradient from light blue to darker blue
            ratio = y / self.window_height
            if ratio < 0.5:
                # Sky
                r = int(135 + (185 - 135) * ratio * 2)
                g = int(206 + (235 - 206) * ratio * 2)
                b = int(235 + (255 - 235) * ratio * 2)
            else:
                # Ground
                ground_ratio = (ratio - 0.5) * 2
                r = int(150 - 50 * ground_ratio)
                g = int(150 - 50 * ground_ratio)
                b = int(120 - 40 * ground_ratio)

            pygame.draw.line(
                self.default_background, (r, g, b), (0, y), (self.window_width, y)
            )

        # Add some building silhouettes
        building_color = (80, 80, 80)
        for i in range(5):
            x = i * self.window_width // 5
            width = self.window_width // 8
            height = np.random.randint(self.window_height // 4, self.window_height // 2)
            y = self.window_height - height
            pygame.draw.rect(
                self.default_background, building_color, (x, y, width, height)
            )

    def load_panorama(self, image_path: str) -> bool:
        """Load a 360-degree panorama image"""
        try:
            if os.path.exists(image_path):
                # Load with PIL first for better format support
                pil_image = Image.open(image_path)
                # Convert to RGB if necessary
                if pil_image.mode != "RGB":
                    pil_image = pil_image.convert("RGB")

                # Convert to pygame surface
                image_string = pil_image.tobytes()
                self.panorama_surface = pygame.image.fromstring(
                    image_string, pil_image.size, "RGB"
                )
                log.info(f"Loaded panorama from {image_path}")
                return True
            else:
                log.warning(f"Panorama file not found: {image_path}")
                return False
        except Exception as e:
            log.error(f"Failed to load panorama: {e}")
            return False

    def render(self, screen: pygame.Surface, camera: Camera):
        """Render the panoramic view based on camera orientation"""
        if self.panorama_surface:
            self._render_panorama(screen, camera)
        else:
            self._render_default(screen, camera)

    def _render_panorama(self, screen: pygame.Surface, camera: Camera):
        """Render actual panorama image"""
        pano_width = self.panorama_surface.get_width()
        pano_height = self.panorama_surface.get_height()

        # Calculate the portion of panorama to display
        # Horizontal: based on yaw and FOV
        fov_ratio = camera.state.fov / 360.0
        view_width = int(pano_width * fov_ratio)

        # Calculate left edge of view in panorama
        yaw_ratio = camera.state.yaw / 360.0
        center_x = int(pano_width * yaw_ratio)
        left_x = center_x - view_width // 2

        # Vertical: based on pitch
        pitch_ratio = (camera.state.pitch + 90) / 180.0  # Convert -90..90 to 0..1
        center_y = int(pano_height * (1 - pitch_ratio))  # Invert for screen coordinates

        # Calculate vertical view range based on FOV and aspect ratio
        aspect_ratio = self.window_width / self.window_height
        vertical_fov = camera.state.fov / aspect_ratio
        vertical_fov_ratio = vertical_fov / 180.0
        view_height = int(pano_height * vertical_fov_ratio)

        top_y = center_y - view_height // 2

        # Create view surface
        view_surface = pygame.Surface((view_width, view_height))

        # Handle wrapping for 360 panorama
        if left_x < 0:
            # Wrap around left edge
            right_part_width = -left_x
            left_part_width = view_width - right_part_width

            # Right part of panorama
            view_surface.blit(
                self.panorama_surface,
                (0, 0),
                (pano_width + left_x, top_y, right_part_width, view_height),
            )
            # Left part of panorama
            view_surface.blit(
                self.panorama_surface,
                (right_part_width, 0),
                (0, top_y, left_part_width, view_height),
            )
        elif left_x + view_width > pano_width:
            # Wrap around right edge
            left_part_width = pano_width - left_x
            right_part_width = view_width - left_part_width

            # Left part of panorama
            view_surface.blit(
                self.panorama_surface,
                (0, 0),
                (left_x, top_y, left_part_width, view_height),
            )
            # Right part of panorama
            view_surface.blit(
                self.panorama_surface,
                (left_part_width, 0),
                (0, top_y, right_part_width, view_height),
            )
        else:
            # No wrapping needed
            view_surface.blit(
                self.panorama_surface, (0, 0), (left_x, top_y, view_width, view_height)
            )

        # Scale to window size
        scaled_surface = pygame.transform.scale(
            view_surface, (self.window_width, self.window_height)
        )
        screen.blit(scaled_surface, (0, 0))

    def _render_default(self, screen: pygame.Surface, camera: Camera):
        """Render default background with rotation effect"""
        # For default background, create a rotating effect
        offset_x = int(
            camera.state.yaw * self.window_width / 90
        )  # Repeat every 90 degrees
        offset_y = int(camera.state.pitch * 5)  # Slight vertical shift

        # Blit with wrapping
        screen.blit(self.default_background, (-offset_x % self.window_width, offset_y))
        screen.blit(
            self.default_background,
            ((-offset_x % self.window_width) - self.window_width, offset_y),
        )


class Environment:
    """Main environment manager"""

    def __init__(self, window_size: Tuple[int, int]):
        self.window_size = window_size
        self.panorama_renderer = PanoramaRenderer(window_size)

        # Visual settings
        self.crosshair_size = 20
        self.pigeon_color = (128, 128, 128)
        self.projectile_color = (255, 255, 0)
        self.crosshair_color = (255, 0, 0)

        # Multi-target plan colors (10 unique colors)
        self.plan_colors = [
            (255, 100, 100),  # Red
            (100, 255, 100),  # Green
            (100, 100, 255),  # Blue
            (255, 255, 100),  # Yellow
            (255, 100, 255),  # Magenta
            (100, 255, 255),  # Cyan
            (255, 165, 0),  # Orange
            (128, 0, 128),  # Purple
            (0, 255, 127),  # Spring Green
            (255, 20, 147),  # Deep Pink
        ]
        self.current_plan_color_index = 0
        self.last_plan_id = None

    def load_panorama(self, path: str):
        """Load panorama image"""
        return self.panorama_renderer.load_panorama(path)

    def render(
        self,
        screen: pygame.Surface,
        camera: Camera,
        entity_manager: EntityManager,
        show_debug: bool = False,
        targeting_system=None,  # Add targeting system for multi-target visualization
    ):
        """Render complete environment"""
        # Render background
        self.panorama_renderer.render(screen, camera)

        # Get current multi-target plan if available
        current_plan = None
        if targeting_system:
            current_plan = targeting_system.get_current_multi_target_plan()

            # Check if this is a new plan
            if (
                current_plan
                and hasattr(current_plan, "targets")
                and len(current_plan.targets) > 0
            ):
                plan_id = id(current_plan)
                if plan_id != self.last_plan_id:
                    self.last_plan_id = plan_id
                    self.current_plan_color_index = (
                        self.current_plan_color_index + 1
                    ) % len(self.plan_colors)

        # Render entities
        self._render_pigeons(screen, camera, entity_manager.pigeons, current_plan)
        self._render_projectiles(screen, camera, entity_manager.projectiles)

        # Render multi-target plan path if active
        if current_plan:
            self._render_multi_target_plan(screen, camera, current_plan)

        # Render predicted positions if enabled
        from config import CONFIG

        if CONFIG.multi_target.SHOW_PREDICTED_POSITIONS and current_plan:
            self._render_predicted_positions(screen, camera, current_plan)

        # Render UI elements
        self._render_crosshair(screen)

        if show_debug:
            self._render_debug_info(screen, camera, entity_manager)

    def _render_pigeons(
        self,
        screen: pygame.Surface,
        camera: Camera,
        pigeons: List[Pigeon],
        current_plan=None,
    ):
        """Render pigeons as gray squares with distance-based sizing"""
        # Build set of planned target IDs for quick lookup
        planned_target_ids = set()
        target_numbers = {}
        if current_plan and hasattr(current_plan, "targets"):
            for i, target_plan in enumerate(current_plan.targets):
                if hasattr(target_plan, "target") and target_plan.target:
                    planned_target_ids.add(target_plan.target.id)
                    target_numbers[target_plan.target.id] = i + 1

        for pigeon in pigeons:
            if not pigeon.active:
                continue

            yaw, pitch, distance = pigeon.to_spherical()
            screen_pos = camera.angles_to_screen(yaw, pitch)

            if screen_pos:
                # Use distance-based apparent size
                apparent_size = int(pigeon.get_apparent_size())

                # Different colors for different states
                if pigeon.is_fleeing:
                    color = (255, 100, 100)  # Red for fleeing
                    border_color = (200, 50, 50)
                elif pigeon.id in planned_target_ids:
                    # Use current plan color for targets in multi-target plan
                    color = self.plan_colors[self.current_plan_color_index]
                    border_color = tuple(max(0, c - 50) for c in color)  # Darker border
                elif pigeon.shots_taken_at > 0:
                    color = (200, 200, 100)  # Yellow for targeted
                    border_color = (150, 150, 50)
                else:
                    color = self.pigeon_color  # Gray for normal
                    border_color = (64, 64, 64)

                # Draw pigeon as square
                rect = pygame.Rect(
                    screen_pos[0] - apparent_size // 2,
                    screen_pos[1] - apparent_size // 2,
                    apparent_size,
                    apparent_size,
                )
                pygame.draw.rect(screen, color, rect)
                pygame.draw.rect(screen, border_color, rect, 2)  # Border

                # Show target number for multi-target plan
                if pigeon.id in target_numbers:
                    font = pygame.font.Font(None, 20)
                    text = font.render(
                        str(target_numbers[pigeon.id]), True, (255, 255, 255)
                    )
                    text_rect = text.get_rect(center=(screen_pos[0], screen_pos[1]))
                    # Add black background for readability
                    bg_rect = text_rect.inflate(4, 2)
                    pygame.draw.rect(screen, (0, 0, 0), bg_rect)
                    screen.blit(text, text_rect)
                # Show shot count for other targeted pigeons
                elif pigeon.shots_taken_at > 0:
                    font = pygame.font.Font(None, 16)
                    text = font.render(
                        str(pigeon.shots_taken_at), True, (255, 255, 255)
                    )
                    text_pos = (
                        screen_pos[0] - 5,
                        screen_pos[1] - apparent_size // 2 - 15,
                    )
                    screen.blit(text, text_pos)

    def _render_projectiles(
        self, screen: pygame.Surface, camera: Camera, projectiles: List[Projectile]
    ):
        """Render projectiles"""
        for projectile in projectiles:
            if not projectile.active:
                continue

            # Convert 3D position to spherical
            pos = projectile.position
            distance = np.linalg.norm(pos)
            if distance < 0.001:
                continue

            yaw = math.degrees(math.atan2(pos[0], pos[2])) % 360
            pitch = math.degrees(math.asin(np.clip(pos[1] / distance, -1, 1)))

            screen_pos = camera.angles_to_screen(yaw, pitch)

            if screen_pos:
                # Draw projectile as small circle
                pygame.draw.circle(screen, self.projectile_color, screen_pos, 3)
                pygame.draw.circle(
                    screen, (255, 255, 255), screen_pos, 4, 1
                )  # White outline

    def _render_crosshair(self, screen: pygame.Surface):
        """Render targeting crosshair"""
        center_x = self.window_size[0] // 2
        center_y = self.window_size[1] // 2

        # Draw crosshair lines
        pygame.draw.line(
            screen,
            self.crosshair_color,
            (center_x - self.crosshair_size, center_y),
            (center_x + self.crosshair_size, center_y),
            2,
        )
        pygame.draw.line(
            screen,
            self.crosshair_color,
            (center_x, center_y - self.crosshair_size),
            (center_x, center_y + self.crosshair_size),
            2,
        )

        # Draw center dot
        pygame.draw.circle(screen, self.crosshair_color, (center_x, center_y), 3)

    def _render_debug_info(
        self, screen: pygame.Surface, camera: Camera, entity_manager: EntityManager
    ):
        """Render debug information"""
        font = pygame.font.Font(None, 24)
        debug_info = [
            f"Camera: Yaw={camera.state.yaw:.1f}° Pitch={camera.state.pitch:.1f}°",
            f"Pigeons: {len([p for p in entity_manager.pigeons if p.active])}",
            f"Projectiles: {len([p for p in entity_manager.projectiles if p.active])}",
        ]

        y_offset = 10
        for line in debug_info:
            text = font.render(line, True, (255, 255, 255))
            text_rect = text.get_rect()

            # Add background for readability
            bg_rect = pygame.Rect(
                10 - 5, y_offset - 2, text_rect.width + 10, text_rect.height + 4
            )
            pygame.draw.rect(screen, (0, 0, 0), bg_rect)
            pygame.draw.rect(screen, (255, 255, 255), bg_rect, 1)

            screen.blit(text, (10, y_offset))
            y_offset += 30

    def _render_predicted_positions(self, screen: pygame.Surface, camera: Camera, plan):
        """Render predicted positions for multi-target plan"""
        if not hasattr(plan, "targets") or len(plan.targets) == 0:
            return

        from config import CONFIG

        cumulative_time = 0.0

        for i, target_plan in enumerate(plan.targets):
            if not hasattr(target_plan, "target") or not target_plan.target:
                continue

            # Calculate time to this target (cumulative)
            cumulative_time += target_plan.rotation_time
            if i > 0:
                cumulative_time += CONFIG.multi_target.SHOT_DELAY

            # Get current position
            current_pos = target_plan.target.position
            current_vel = target_plan.target.velocity

            # Calculate predicted position at engagement time
            predicted_pos = current_pos + current_vel * cumulative_time

            # Convert to screen coordinates
            distance = np.linalg.norm(predicted_pos)
            if distance < 0.001:
                continue

            yaw = math.degrees(math.atan2(predicted_pos[0], predicted_pos[2])) % 360
            pitch = math.degrees(math.asin(np.clip(predicted_pos[1] / distance, -1, 1)))

            screen_pos = camera.angles_to_screen(yaw, pitch)

            if screen_pos:
                # Draw prediction box (no fill)
                box_size = 40
                box_rect = pygame.Rect(
                    screen_pos[0] - box_size // 2,
                    screen_pos[1] - box_size // 2,
                    box_size,
                    box_size,
                )
                pygame.draw.rect(
                    screen, CONFIG.multi_target.PREDICTION_BOX_COLOR, box_rect, 2
                )

                # Draw target number
                font = pygame.font.Font(None, 24)
                number_text = font.render(
                    str(i + 1), True, CONFIG.multi_target.PREDICTION_BOX_COLOR
                )
                number_pos = (screen_pos[0] - 10, screen_pos[1] - 10)
                screen.blit(number_text, number_pos)

                # Draw time to target
                time_font = pygame.font.Font(None, 18)
                time_text = time_font.render(
                    f"t={cumulative_time:.3f}s",
                    True,
                    CONFIG.multi_target.PREDICTION_BOX_COLOR,
                )
                time_pos = (screen_pos[0] - 30, screen_pos[1] + box_size // 2 + 5)

                # Add background for readability
                time_bg_rect = time_text.get_rect()
                time_bg_rect.topleft = time_pos
                time_bg_rect.inflate_ip(4, 2)
                pygame.draw.rect(screen, (0, 0, 0), time_bg_rect)

                screen.blit(time_text, time_pos)

    def _render_multi_target_plan(self, screen: pygame.Surface, camera: Camera, plan):
        """Render the multi-target engagement plan path"""
        if not hasattr(plan, "targets") or len(plan.targets) < 2:
            return

        # Get current plan color
        plan_color = self.plan_colors[self.current_plan_color_index]

        # Calculate lighter color for predicted positions
        predicted_color = tuple(min(255, c + 50) for c in plan_color)

        # Draw lines between targets in sequence
        prev_screen_pos = None
        prev_predicted_pos = None

        for i, target_plan in enumerate(plan.targets):
            if (
                hasattr(target_plan, "target")
                and target_plan.target
                and target_plan.target.active
            ):
                # Current position
                yaw, pitch, _ = target_plan.target.to_spherical()
                screen_pos = camera.angles_to_screen(yaw, pitch)

                # Predicted position (if available)
                predicted_screen_pos = None
                if (
                    hasattr(target_plan, "predicted_position")
                    and target_plan.predicted_position
                ):
                    pred_x, pred_y, pred_z = target_plan.predicted_position

                    # Convert to spherical
                    pred_distance = math.sqrt(pred_x**2 + pred_y**2 + pred_z**2)
                    if pred_distance > 0.001:
                        pred_yaw = math.degrees(math.atan2(pred_x, pred_z)) % 360
                        pred_pitch = math.degrees(
                            math.asin(np.clip(pred_y / pred_distance, -1, 1))
                        )
                        predicted_screen_pos = camera.angles_to_screen(
                            pred_yaw, pred_pitch
                        )

                if screen_pos:
                    # Draw current path
                    if prev_screen_pos:
                        self._draw_dotted_line(
                            screen, prev_screen_pos, screen_pos, plan_color, 2
                        )

                    # Draw predicted path
                    if predicted_screen_pos and prev_predicted_pos:
                        self._draw_dotted_line(
                            screen,
                            prev_predicted_pos,
                            predicted_screen_pos,
                            predicted_color,
                            1,
                        )

                    # Draw arrow from current to predicted position
                    if predicted_screen_pos:
                        self._draw_arrow(
                            screen, screen_pos, predicted_screen_pos, predicted_color, 1
                        )

                prev_screen_pos = screen_pos
                prev_predicted_pos = predicted_screen_pos

    def _draw_dotted_line(self, screen, start_pos, end_pos, color, width):
        """Draw a dotted line between two points"""
        import math

        dx = end_pos[0] - start_pos[0]
        dy = end_pos[1] - start_pos[1]
        distance = math.sqrt(dx**2 + dy**2)

        if distance < 1:
            return

        dots = int(distance / 10)  # Dot every 10 pixels
        for i in range(dots):
            t = i / dots
            x = int(start_pos[0] + dx * t)
            y = int(start_pos[1] + dy * t)
            pygame.draw.circle(screen, color, (x, y), width)

    def _draw_arrow(self, screen, start_pos, end_pos, color, width):
        """Draw an arrow from start to end position"""
        if not start_pos or not end_pos:
            return

        dx = end_pos[0] - start_pos[0]
        dy = end_pos[1] - start_pos[1]
        distance = math.sqrt(dx**2 + dy**2)

        if distance < 5:
            return

        # Draw line
        pygame.draw.line(screen, color, start_pos, end_pos, width)

        # Draw arrowhead
        angle = math.atan2(dy, dx)
        arrow_length = min(10, distance / 3)
        arrow_angle = math.pi / 6  # 30 degrees

        # Calculate arrowhead points
        x1 = end_pos[0] - arrow_length * math.cos(angle - arrow_angle)
        y1 = end_pos[1] - arrow_length * math.sin(angle - arrow_angle)
        x2 = end_pos[0] - arrow_length * math.cos(angle + arrow_angle)
        y2 = end_pos[1] - arrow_length * math.sin(angle + arrow_angle)

        pygame.draw.line(screen, color, end_pos, (int(x1), int(y1)), width)
        pygame.draw.line(screen, color, end_pos, (int(x2), int(y2)), width)
