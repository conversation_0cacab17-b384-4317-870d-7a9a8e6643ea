"""Test different targeting algorithms"""

import subprocess
import time
import json
import os


def test_algorithm(algorithm_name, duration=15):
    """Test a specific algorithm for the given duration"""
    print(f"\nTesting {algorithm_name} algorithm for {duration} seconds...")

    # Modify config to use the specified algorithm
    with open("config.py", "r") as f:
        config_content = f.read()

    # Replace the targeting mode
    modified_config = config_content.replace(
        'TARGETING_MODE: str = "predictive"',
        f'TARGETING_MODE: str = "{algorithm_name}"',
    )

    # Write back
    with open("config.py", "w") as f:
        f.write(modified_config)

    # Run simulation
    proc = subprocess.Popen(["uv", "run", "main.py"])
    time.sleep(duration)
    proc.terminate()
    proc.wait()

    # Read metrics
    if os.path.exists("turret_metrics.json"):
        with open("turret_metrics.json", "r") as f:
            data = json.load(f)

        # Extract key metrics
        session = data["session"]
        algo_data = data["algorithms"].get(algorithm_name, {})

        print(f"\nResults for {algorithm_name}:")
        print(f"  Pigeons spawned: {session['total_pigeons_spawned']}")
        print(f"  Pigeons hit: {session['total_pigeons_hit']}")
        print(f"  Shots fired: {session['total_shots_fired']}")
        if algo_data:
            print(f"  Accuracy: {algo_data['accuracy']:.1f}%")
            print(f"  Avg movement time: {algo_data['avg_movement_time']:.2f}s")
            print(f"  Avg reaction time: {algo_data['avg_reaction_time']:.2f}s")

        return algo_data.get("accuracy", 0) if algo_data else 0

    return 0


def main():
    """Test all algorithms and compare"""
    algorithms = ["nearest", "predictive", "opportunistic"]
    results = {}

    for algo in algorithms:
        accuracy = test_algorithm(algo, duration=15)
        results[algo] = accuracy
        time.sleep(2)  # Brief pause between tests

    # Restore original config
    with open("config.py", "r") as f:
        config_content = f.read()

    for algo in algorithms:
        config_content = config_content.replace(
            f'TARGETING_MODE: str = "{algo}"', 'TARGETING_MODE: str = "predictive"'
        )

    with open("config.py", "w") as f:
        f.write(config_content)

    # Summary
    print("\n=== ALGORITHM COMPARISON ===")
    for algo, accuracy in results.items():
        print(f"{algo}: {accuracy:.1f}% accuracy")

    best_algo = max(results, key=results.get)
    print(f"\nBest algorithm: {best_algo} ({results[best_algo]:.1f}% accuracy)")


if __name__ == "__main__":
    main()
