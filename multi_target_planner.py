"""Multi-target engagement planner for the pigeon turret simulator"""

import time
import math
import numpy as np
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass
from loguru import logger as log

from entities import Pigeon
from config import CONFIG


@dataclass
class TargetPlan:
    """Represents a planned engagement for a single target"""

    target: Pigeon
    engagement_time: float  # Time when we'll shoot
    predicted_position: Tuple[float, float, float]  # Where target will be (3D)
    rotation_time: float  # Time to rotate to target
    confidence: float  # 0-1 confidence in hitting
    actual_position: Optional[Tuple[float, float, float]] = (
        None  # Actual position when fired (3D)
    )
    prediction_error: Optional[float] = None  # Distance between predicted and actual


@dataclass
class EngagementPlan:
    """Complete multi-target engagement plan"""

    targets: List[TargetPlan]
    total_time: float
    total_rotation: float
    planning_time: float
    algorithm_used: str


class MultiTargetPlanner:
    """Plans and optimizes multi-target engagement sequences"""

    def __init__(self):
        self.config = CONFIG.multi_target
        self.turret_rotation_speed = self.config.TURRET_ROTATION_SPEED
        self.shot_delay = self.config.SHOT_DELAY
        self.last_plan: Optional[EngagementPlan] = None
        self.planning_stats = {
            "total_plans": 0,
            "successful_plans": 0,
            "fallback_count": 0,
            "avg_planning_time": 0.0,
            "avg_targets_planned": 0.0,
        }

    def plan_engagement(
        self, targets: List[Pigeon], current_turret_angle: float, current_time: float
    ) -> Optional[EngagementPlan]:
        """Main planning method - returns engagement plan or None"""
        if not self.config.MULTI_TARGET_ENABLED:
            log.debug("Multi-target disabled")
            return None

        # Start timing
        start_time = time.perf_counter()

        # Filter valid targets
        valid_targets = self._filter_valid_targets(targets)
        log.debug(
            f"Filtered {len(targets)} targets to {len(valid_targets)} valid targets"
        )

        if len(valid_targets) < self.config.MIN_TARGETS_FOR_MULTI:
            log.debug(
                f"Not enough valid targets: {len(valid_targets)} < {self.config.MIN_TARGETS_FOR_MULTI}"
            )
            return None

        # Select best targets
        selected_targets = self._select_targets(valid_targets, current_turret_angle)
        log.debug(f"Selected {len(selected_targets)} targets for planning")

        if not selected_targets:
            log.debug("No targets selected")
            return None

        # Choose algorithm based on target count and available time
        algorithm = self._choose_algorithm(len(selected_targets))
        log.debug(f"Chosen algorithm: {algorithm}")

        # Optimize firing sequence
        try:
            log.debug("Starting path optimization...")
            firing_sequence = self._optimize_path(
                selected_targets, current_turret_angle, algorithm, start_time
            )
            log.debug(f"Path optimization complete: {len(firing_sequence)} targets in sequence")
        except TimeoutError:
            log.warning("Path planning timeout - falling back to simple algorithm")
            self.planning_stats["fallback_count"] += 1
            firing_sequence = self._optimize_path_greedy(
                selected_targets[:3],  # Reduce targets for speed
                current_turret_angle,
            )
            algorithm = "greedy_fallback"

        # Generate time-parameterized plan
        log.debug("Generating engagement plan...")
        plan = self._generate_plan(
            firing_sequence, current_turret_angle, current_time, algorithm
        )
        log.debug("Plan generation complete")

        # Update stats
        planning_time = time.perf_counter() - start_time
        plan.planning_time = planning_time
        self._update_stats(plan)

        self.last_plan = plan
        return plan

    def _filter_valid_targets(self, targets: List[Pigeon]) -> List[Pigeon]:
        """Filter targets that are valid for engagement"""
        valid = []
        for target in targets:
            if target.is_fleeing or not target.active:
                continue

            distance = math.sqrt(
                target.position[0] ** 2
                + target.position[1] ** 2
                + target.position[2] ** 2
            )
            if distance > CONFIG.targeting.MAX_ENGAGEMENT_DISTANCE:
                continue

            # Check if target is moving away too fast
            radial_velocity = (
                target.velocity[0] * target.position[0]
                + target.velocity[1] * target.position[1]
                + target.velocity[2] * target.position[2]
            ) / distance
            if radial_velocity > 200:  # Moving away rapidly
                continue

            valid.append(target)

        return valid

    def _select_targets(
        self, targets: List[Pigeon], current_angle: float
    ) -> List[Pigeon]:
        """Score and select best targets for engagement"""
        scores = []

        for target in targets:
            score = self._score_target(target, current_angle, targets, 0.0)
            scores.append((score, target))

        # Sort by score and take top N
        scores.sort(reverse=True, key=lambda x: x[0])
        max_targets = min(self.config.MULTI_TARGET_MAX, len(scores))

        return [target for _, target in scores[:max_targets]]

    def _score_target(
        self,
        target: Pigeon,
        current_angle: float,
        all_targets: List[Pigeon],
        current_time: float,
        cumulative_time: float = 0.0,  # Time already spent on previous targets
    ) -> float:
        """Score a target for selection based on multiple factors"""
        # Predict where target will be when we engage it
        rotation_time = self._calculate_rotation_time(
            current_angle,
            math.degrees(math.atan2(target.position[0], target.position[2])),
        )
        engagement_time = cumulative_time + rotation_time + self.config.SHOT_DELAY

        # Predict future position
        future_pos = target.position + target.velocity * engagement_time
        future_distance = math.sqrt(
            future_pos[0] ** 2 + future_pos[1] ** 2 + future_pos[2] ** 2
        )

        # Distance score (based on predicted position)
        distance_score = 1.0 / (1.0 + future_distance / 500.0)

        # Threat score (velocity towards origin)
        radial_velocity = np.dot(target.velocity, target.position) / np.linalg.norm(
            target.position
        )
        threat_score = 1.0 / (1.0 + max(0, radial_velocity) / 100.0)

        # Angular proximity (prefer targets near current angle)
        target_angle = math.degrees(math.atan2(future_pos[0], future_pos[2]))
        angle_diff = abs(self._normalize_angle(target_angle - current_angle))
        angle_score = 1.0 / (1.0 + angle_diff / 45.0)

        # Cluster score with velocity alignment
        cluster_score = 0.0
        velocity_alignment_score = 0.0
        nearby_count = 0

        for other in all_targets:
            if other.id != target.id:
                # Predict other's future position
                other_future = other.position + other.velocity * engagement_time
                dx = future_pos[0] - other_future[0]
                dy = future_pos[1] - other_future[1]
                dz = future_pos[2] - other_future[2]
                future_distance_between = math.sqrt(dx**2 + dy**2 + dz**2)

                if future_distance_between < self.config.CLUSTER_RADIUS:
                    cluster_score += 1.0 / (1.0 + future_distance_between / 100.0)

                    # Velocity alignment - targets moving in same direction
                    vel_dot = np.dot(target.velocity, other.velocity)
                    vel_mag = np.linalg.norm(target.velocity) * np.linalg.norm(
                        other.velocity
                    )
                    if vel_mag > 0:
                        alignment = (
                            vel_dot / vel_mag
                        )  # -1 to 1, where 1 is same direction
                        velocity_alignment_score += max(0, alignment)

                    nearby_count += 1

        if nearby_count > 0:
            cluster_score = cluster_score / nearby_count
            velocity_alignment_score = velocity_alignment_score / nearby_count

        cluster_score = min(1.0, cluster_score)

        # Hit probability (based on predicted position and speed)
        speed = np.linalg.norm(target.velocity)
        hit_probability = 1.0 / (1.0 + speed / 200.0 + future_distance / 1000.0)

        # Combined score with weights
        score = (
            distance_score * self.config.WEIGHT_DISTANCE
            + threat_score * self.config.WEIGHT_THREAT
            + cluster_score * self.config.WEIGHT_CLUSTER * 0.7  # Reduced weight
            + velocity_alignment_score
            * self.config.WEIGHT_CLUSTER
            * 0.3  # New component
            + hit_probability * self.config.WEIGHT_HIT_PROBABILITY
            + angle_score * 0.2  # Bonus for minimal rotation
        )

        return score

    def _choose_algorithm(self, target_count: int) -> str:
        """Choose path optimization algorithm based on target count"""
        if self.config.PATH_ALGORITHM != "adaptive":
            return self.config.PATH_ALGORITHM

        if target_count <= 4:
            return "optimal"  # Can do exhaustive search
        elif target_count <= 7:
            return "angle_sort"  # Fast and decent
        else:
            return "greedy"  # Fastest for many targets

    def _optimize_path(
        self,
        targets: List[Pigeon],
        start_angle: float,
        algorithm: str,
        start_time: float,
    ) -> List[Pigeon]:
        """Optimize firing sequence using specified algorithm"""
        # Check timeout
        if time.perf_counter() - start_time > self.config.PATH_PLANNING_TIMEOUT:
            raise TimeoutError()

        if algorithm == "greedy":
            return self._optimize_path_greedy(targets, start_angle)
        elif algorithm == "angle_sort":
            return self._optimize_path_angle_sort(targets, start_angle)
        elif algorithm == "optimal":
            return self._optimize_path_optimal(targets, start_angle, start_time)
        else:
            return self._optimize_path_greedy(targets, start_angle)

    def _optimize_path_greedy(
        self, targets: List[Pigeon], start_angle: float
    ) -> List[Pigeon]:
        """Greedy nearest-neighbor path optimization with trajectory prediction"""
        log.debug(f"Starting greedy optimization with {len(targets)} targets")

        path = []
        current_angle = start_angle
        remaining_ids = [t.id for t in targets]  # Track by ID
        target_dict = {t.id: t for t in targets}  # ID to target mapping
        current_time = 0.0

        # Safety counter to prevent infinite loops
        max_iterations = len(targets) * 2
        iteration_count = 0

        while remaining_ids and iteration_count < max_iterations:
            iteration_count += 1
            log.debug(
                f"Greedy iteration {iteration_count}, remaining targets: {len(remaining_ids)}"
            )

            best_target_id = None
            best_cost = float("inf")

            for target_id in remaining_ids:
                target = target_dict[target_id]

                # Predict where target will be after rotation
                target_angle = math.degrees(
                    math.atan2(target.position[0], target.position[2])
                )
                rotation_time = self._calculate_rotation_time(
                    current_angle, target_angle
                )

                # Predict future position
                future_pos = target.position + target.velocity * (
                    current_time + rotation_time
                )
                future_angle = math.degrees(math.atan2(future_pos[0], future_pos[2]))

                # Actual rotation time to future position
                actual_rotation_time = self._calculate_rotation_time(
                    current_angle, future_angle
                )

                # Cost includes rotation time and distance
                future_distance = math.sqrt(
                    future_pos[0] ** 2 + future_pos[1] ** 2 + future_pos[2] ** 2
                )
                cost = (
                    actual_rotation_time + future_distance / 1000.0
                )  # Balance time and distance

                if cost < best_cost:
                    best_cost = cost
                    best_target_id = target_id

            if best_target_id:
                best_target = target_dict[best_target_id]
                path.append(best_target)
                remaining_ids.remove(best_target_id)
                log.debug(f"Selected target {best_target_id}, cost: {best_cost:.3f}")

                # Update current angle and time
                future_pos = best_target.position + best_target.velocity * (
                    current_time + best_cost
                )
                current_angle = math.degrees(math.atan2(future_pos[0], future_pos[2]))
                current_time += best_cost + self.config.SHOT_DELAY
            else:
                log.warning("No best target found in greedy optimization - breaking")
                break

        if iteration_count >= max_iterations:
            log.warning(f"Greedy optimization hit max iterations ({max_iterations})")

        log.debug(f"Greedy optimization complete: {len(path)} targets selected")
        return path

    def _optimize_path_angle_sort(
        self, targets: List[Pigeon], start_angle: float
    ) -> List[Pigeon]:
        """Sort targets by angle to minimize total rotation"""
        # Calculate angles for all targets
        target_angles = []
        for target in targets:
            angle = math.degrees(math.atan2(target.position[0], target.position[2]))
            # Normalize relative to start angle
            rel_angle = self._normalize_angle(angle - start_angle)
            target_angles.append((rel_angle, target))

        # Sort by angle
        target_angles.sort(key=lambda x: x[0])

        # Return sorted targets
        return [target for _, target in target_angles]

    def _optimize_path_optimal(
        self, targets: List[Pigeon], start_angle: float, start_time: float
    ) -> List[Pigeon]:
        """Find optimal path for small target sets using dynamic programming"""
        n = len(targets)
        if n > 8:  # Too many for optimal search
            return self._optimize_path_greedy(targets, start_angle)

        # Build cost matrix
        angles = [start_angle]
        for target in targets:
            angles.append(
                math.degrees(math.atan2(target.position[0], target.position[2]))
            )

        # Simple TSP solver for small n
        best_path = None
        best_cost = float("inf")

        # Try different starting targets
        for i in range(n):
            if (
                time.perf_counter() - start_time
                > self.config.PATH_PLANNING_TIMEOUT * 0.8
            ):
                break

            path = [targets[i]]
            remaining_indices = list(range(n))
            remaining_indices.remove(i)
            current_angle = angles[i + 1]
            cost = self._calculate_rotation_time(start_angle, current_angle)

            # Greedy fill rest of path
            while remaining_indices:
                best_next_idx = None
                best_next_cost = float("inf")

                for idx in remaining_indices:
                    target = targets[idx]
                    target_angle = math.degrees(
                        math.atan2(target.position[0], target.position[2])
                    )
                    rotation_cost = self._calculate_rotation_time(
                        current_angle, target_angle
                    )

                    if rotation_cost < best_next_cost:
                        best_next_cost = rotation_cost
                        best_next_idx = idx

                if best_next_idx is not None:
                    path.append(targets[best_next_idx])
                    remaining_indices.remove(best_next_idx)
                    current_angle = math.degrees(
                        math.atan2(
                            targets[best_next_idx].position[0],
                            targets[best_next_idx].position[2],
                        )
                    )
                    cost += best_next_cost

            if cost < best_cost:
                best_cost = cost
                best_path = path

        return best_path or targets

    def _generate_plan(
        self,
        targets: List[Pigeon],
        start_angle: float,
        current_time: float,
        algorithm: str,
    ) -> EngagementPlan:
        """Generate time-parameterized engagement plan"""
        target_plans = []
        current_angle = start_angle
        time_offset = 0.0
        total_rotation = 0.0

        for i, target in enumerate(targets):
            # Calculate rotation to target
            target_angle = math.degrees(
                math.atan2(target.position[0], target.position[2])
            )
            rotation_time = self._calculate_rotation_time(current_angle, target_angle)
            rotation_amount = abs(self._normalize_angle(target_angle - current_angle))

            # Predict target position at engagement time
            engagement_time = current_time + time_offset + rotation_time
            future_x = target.position[0] + target.velocity[0] * (
                time_offset + rotation_time
            )
            future_y = target.position[1] + target.velocity[1] * (
                time_offset + rotation_time
            )
            future_z = target.position[2] + target.velocity[2] * (
                time_offset + rotation_time
            )

            # Calculate confidence based on various factors
            distance = math.sqrt(future_x**2 + future_y**2 + future_z**2)
            speed = np.linalg.norm(target.velocity)
            confidence = self._calculate_hit_confidence(distance, speed, rotation_time)

            target_plans.append(
                TargetPlan(
                    target=target,
                    engagement_time=engagement_time,
                    predicted_position=(future_x, future_y, future_z),
                    rotation_time=rotation_time,
                    confidence=confidence,
                )
            )

            # Update for next target
            current_angle = target_angle
            time_offset += rotation_time + self.shot_delay
            total_rotation += rotation_amount

        return EngagementPlan(
            targets=target_plans,
            total_time=time_offset,
            total_rotation=total_rotation,
            planning_time=0.0,  # Will be set by caller
            algorithm_used=algorithm,
        )

    def _calculate_rotation_time(self, from_angle: float, to_angle: float) -> float:
        """Calculate time to rotate turret between angles"""
        angle_diff = abs(self._normalize_angle(to_angle - from_angle))
        return angle_diff / self.turret_rotation_speed

    def _normalize_angle(self, angle: float) -> float:
        """Normalize angle to [-180, 180] range"""
        while angle > 180:
            angle -= 360
        while angle < -180:
            angle += 360
        return angle

    def _calculate_hit_confidence(
        self, distance: float, speed: float, prediction_time: float
    ) -> float:
        """Calculate confidence in hitting target"""
        # Base confidence decreases with distance
        base_confidence = 1.0 / (1.0 + distance / 800.0)

        # Reduce confidence for fast targets
        speed_penalty = 1.0 / (1.0 + speed / 200.0)

        # Reduce confidence for long prediction times
        time_penalty = 1.0 / (1.0 + prediction_time / 2.0)

        return base_confidence * speed_penalty * time_penalty

    def _update_stats(self, plan: EngagementPlan):
        """Update planning statistics"""
        self.planning_stats["total_plans"] += 1
        self.planning_stats["successful_plans"] += 1

        # Update moving averages
        alpha = 0.1  # Smoothing factor
        self.planning_stats["avg_planning_time"] = (1 - alpha) * self.planning_stats[
            "avg_planning_time"
        ] + alpha * plan.planning_time
        self.planning_stats["avg_targets_planned"] = (1 - alpha) * self.planning_stats[
            "avg_targets_planned"
        ] + alpha * len(plan.targets)

    def get_stats(self) -> Dict:
        """Get planning statistics"""
        return self.planning_stats.copy()
