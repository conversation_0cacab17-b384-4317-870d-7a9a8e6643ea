"""Configuration for Pigeon Turret Simulator"""

import sys
from dataclasses import dataclass
from typing import Tuple
from loguru import logger as log


@dataclass
class EnvironmentConfig:
    """Environment and rendering configuration"""

    # Window Settings
    WINDOW_WIDTH: int = 1920
    WINDOW_HEIGHT: int = 1080
    FPS: int = 60

    # Camera Settings
    CAMERA_FOV: float = 130.0  # Increased from 90 to 130 degrees
    CAMERA_SENSITIVITY: float = 0.5
    TURRET_MAX_SPEED: float = 180.0  # degrees per second
    TURRET_ACCELERATION: float = 360.0  # degrees per second^2

    # Pigeon Settings
    PIGEON_SIZE: int = 30
    PIGEON_SPEED_MIN: float = 150.0
    PIGEON_SPEED_MAX: float = 300.0
    PIGEON_SPAWN_RATE: float = 7.0  # Increased from 10 to 15 pigeons per second
    MAX_PIGEONS: int = 100
    PIGEON_FLEE_SPEED: float = 200.0  # Speed when fleeing after being hit
    PIGEON_FLEE_DISTANCE: float = 2500.0  # Distance before pigeon is removed

    # Enhanced Pigeon Management
    MIN_PIGEONS_IN_RANGE: int = 6  # Minimum pigeons within engagement range
    OPTIMAL_PIGEONS_IN_RANGE: int = 8  # Target number of pigeons in range
    PIGEON_CULL_DISTANCE: float = 1800.0  # Distance at which to cull pigeons
    DYNAMIC_SPAWN_COOLDOWN: float = 2.0  # Seconds between dynamic spawn checks
    PIGEON_RELEVANCE_CHECK: bool = True  # Ensure pigeons cross turret range
    PIGEON_OUT_OF_RANGE_TIMEOUT: float = (
        10.0  # Seconds before culling out-of-range pigeons
    )

    # Turret Search Behavior
    TURRET_SEARCH_ENABLED: bool = True  # Enable automatic search when no targets
    TURRET_SEARCH_TIMEOUT: float = 5.0  # Seconds with no targets before searching
    TURRET_SEARCH_ANGLE: float = 180.0  # Initial search angle (degrees)
    TURRET_SEARCH_REDUCE_TIMEOUT: float = 15.0  # Seconds before reducing search angle
    TURRET_SEARCH_REDUCED_ANGLE: float = 90.0  # Reduced search angle

    # Environment Settings
    PANORAMA_PATH: str = "assets/balcony_360.jpg"
    CROSSHAIR_COLOR: Tuple[int, int, int] = (255, 0, 0)
    PIGEON_COLOR: Tuple[int, int, int] = (128, 128, 128)

    # Debug Settings
    DEBUG_MODE: bool = True
    LOG_LEVEL: str = "INFO"  # Reduced from DEBUG
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"


@dataclass
class KPIWeights:
    """Weights for different KPIs in optimization scoring"""

    # Core performance metrics
    accuracy: float = 0.4  # Weight for hit/shot ratio
    hits_per_minute: float = 0.3  # Weight for absolute hit rate
    engagement_rate: float = 0.2  # Weight for shots fired per target available
    efficiency: float = 0.1  # Weight for shots per hit (inverted)

    # Secondary metrics
    movement_time: float = 0.05  # Weight for turret responsiveness
    reaction_time: float = 0.05  # Weight for target acquisition speed

    # Penalty factors
    missed_opportunities: float = -0.1  # Penalty for targets that escape unengaged
    wasted_shots: float = -0.05  # Penalty for excessive shots at same target


@dataclass
class TargetingConfig:
    """Targeting and weapon system configuration"""

    # Core Targeting
    TARGETING_MODE: str = "predictive"  # nearest, predictive, opportunistic
    PROJECTILE_SPEED: float = 500.0  # pixels per second
    HIT_RADIUS: float = 15.0
    MAX_ENGAGEMENT_DISTANCE: float = 1200.0  # Maximum distance to engage targets

    # Fire Control
    FIRE_COOLDOWN: float = 0.3  # seconds between shots
    FIRE_COOLDOWN_CLOSE: float = 0.2  # Faster fire rate for close targets
    CLOSE_TARGET_DISTANCE: float = 500.0  # Distance threshold for fast fire rate
    MAX_SHOTS_PER_TARGET: int = 3  # Maximum shots per pigeon before switching
    MAX_SHOTS_PER_SECOND: float = 3.0  # Maximum fire rate

    # Targeting Precision
    ANGLE_THRESHOLD_CLOSE: float = 3.0  # Degrees - for targets < 600 units
    ANGLE_THRESHOLD_FAR: float = 8.0  # Degrees - for targets > 600 units

    # Burst Fire Settings
    BURST_FIRE_DISTANCE: float = 250.0  # Distance threshold for burst fire
    BURST_FIRE_COUNT: int = 1  # Number of additional shots in burst
    BURST_FIRE_INTERVAL: float = 0.1  # Time between burst shots
    BURST_FIRE_MAX_SPEED: float = 80.0  # Only burst fire on slow targets


@dataclass
class MultiTargetConfig:
    """Multi-target engagement system configuration"""

    # Core Multi-Target Settings
    MULTI_TARGET_ENABLED: bool = True                      # Re-enabled - system is working correctly
    MULTI_TARGET_MAX: int = 5                              # Reduced from 10 for testing

    # Turret Movement
    TURRET_ROTATION_SPEED: float = 180.0                   # degrees/second (matches EnvironmentConfig)
    SHOT_DELAY: float = 0.2                                # Increased from 0.1 for more reliable firing

    # Path Planning
    PATH_PLANNING_TIMEOUT: float = 0.008                   # 8ms max planning time
    PATH_ALGORITHM: str = "greedy"                         # Start with simple greedy algorithm

    # Target Selection Weights
    WEIGHT_DISTANCE: float = 0.4                           # Weight for proximity
    WEIGHT_THREAT: float = 0.3                             # Weight for threat level (velocity toward center)
    WEIGHT_CLUSTER: float = 0.2                            # Weight for target clustering
    WEIGHT_HIT_PROBABILITY: float = 0.1                    # Weight for hit probability

    # Performance Thresholds
    MIN_TARGETS_FOR_MULTI: int = 3                         # Minimum targets to activate multi-target mode
    MAX_ROTATION_PER_TARGET: float = 45.0                  # Max degrees to rotate for next target
    CLUSTER_RADIUS: float = 200.0                          # Radius to consider targets clustered

    # Visualization
    SHOW_PLANNED_PATH: bool = True                          # Display planned engagement path
    PATH_COLOR: Tuple[int, int, int] = (255, 255, 0)       # Yellow for path
    PATH_ALPHA: float = 0.5                                 # Transparency for path visualization
    SHOW_TARGET_NUMBERS: bool = True                        # Number targets in sequence

    # Fallback Settings
    FALLBACK_TO_SINGLE: bool = True                         # Revert to single-target if planning fails
    MAX_PLANNING_ATTEMPTS: int = 2                          # Attempts before fallback


@dataclass
class BrainConfig:
    """AI Brain configuration for live optimization"""

    # Brain Control
    BRAIN_ENABLED: bool = False                             # Temporarily disabled for testing
    BRAIN_UPDATE_INTERVAL: float = 15.0                     # Seconds between brain updates
    BRAIN_SCREENSHOT_INTERVAL: float = 3.0                  # Seconds between screenshots
    BRAIN_MAX_SCREENSHOTS: int = 5                          # Maximum screenshots to send per update

    # OpenAI Configuration
    OPENAI_MODEL: str = "gpt-4o"                            # Model to use
    OPENAI_MAX_TOKENS: int = 1500                           # Increased for more detailed analysis
    OPENAI_TEMPERATURE: float = 0.3                         # Lower for more consistent optimization

    # Brain Learning
    BRAIN_MEMORY_SIZE: int = 10                             # Number of previous optimizations to remember
    BRAIN_MIN_SAMPLES: int = 3                              # Minimum performance samples before optimization
    BRAIN_CONSERVATIVE_MODE: bool = True                    # Make smaller adjustments

    # Experiment Configuration
    EXPERIMENT_DURATION: float = 30.0                       # Seconds per experiment
    EXPERIMENT_COOLDOWN: float = 5.0                        # Seconds between experiments
    AUTO_EXPERIMENT_MODE: bool = True                       # Enable automatic experiments
    SAVE_EXPERIMENT_CONFIGS: bool = True                    # Save successful configurations
    MAX_EXPERIMENT_CONFIGS: int = 20                        # Maximum saved configurations

    # Enhanced Logging for AI
    LOG_TARGET_ESCAPES: bool = True                         # Log when targets escape without engagement
    LOG_SHOT_EFFICIENCY: bool = True                        # Log detailed shot efficiency metrics
    LOG_MOVEMENT_PATTERNS: bool = True                      # Log turret movement patterns
    LOG_ENGAGEMENT_WINDOWS: bool = True                     # Log missed engagement opportunities
    LOG_ALGORITHM_SWITCHING: bool = True                    # Log when algorithms are changed
    LOG_TO_FILE: bool = True                                # Save AI logs to file
    LOG_FILE_PATH: str = "ai_brain_logs.jsonl"              # Log file path

    # KPI Optimization
    KPI_WEIGHTS: KPIWeights = None                          # Will be set in __post_init__

    # Parameter Bounds (for safety)
    MIN_FIRE_COOLDOWN: float = 0.1
    MAX_FIRE_COOLDOWN: float = 1.0
    MIN_ANGLE_THRESHOLD: float = 1.0
    MAX_ANGLE_THRESHOLD: float = 15.0
    MIN_ENGAGEMENT_DISTANCE: float = 500.0
    MAX_ENGAGEMENT_DISTANCE: float = 2000.0

    def __post_init__(self):
        if self.KPI_WEIGHTS is None:
            self.KPI_WEIGHTS = KPIWeights()


@dataclass
class Config:
    """Main configuration container"""

    def __init__(self):
        self.environment = EnvironmentConfig()
        self.targeting = TargetingConfig()
        self.multi_target = MultiTargetConfig()
        self.brain = BrainConfig()

    def log_init(self):
        """Initialize loguru with color formatting."""
        log.remove()  # Remove default handler
        log.add(
            sys.stderr,
            format=self.environment.LOG_FORMAT,
            level=self.environment.LOG_LEVEL,
        )


# Global config instance
CONFIG = Config()
