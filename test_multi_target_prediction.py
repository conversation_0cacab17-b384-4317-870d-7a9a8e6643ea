"""Unit test for multi-target prediction logic"""

import unittest
import numpy as np
import time
from unittest.mock import Mock

from entities import Pigeon
from multi_target_planner import MultiTargetPlanner, TargetPlan, EngagementPlan
from config import CONFIG


class TestMultiTargetPrediction(unittest.TestCase):
    """Test multi-target prediction with compound timing"""

    def setUp(self):
        """Set up test environment"""
        self.planner = MultiTargetPlanner()
        self.current_time = time.time()

        # Create 5 test pigeons at different positions and velocities
        self.pigeons = []
        for i in range(5):
            pigeon = Pigeon(
                position=np.array(
                    [100.0 + i * 200, 50.0, 300.0 + i * 100], dtype=float
                ),
                velocity=np.array([50.0, 0.0, -30.0], dtype=float),
                size=30,
                active=True,
                id=i,
                spawn_time=self.current_time,
            )
            self.pigeons.append(pigeon)

    def test_compound_prediction_timing(self):
        """Test that predictions account for cumulative time correctly"""
        # Create a mock engagement plan with 5 targets
        target_plans = []
        cumulative_time = 0.0

        for i, pigeon in enumerate(self.pigeons):
            # Each target takes 0.5s to rotate to + 0.2s shot delay
            rotation_time = 0.5
            if i > 0:
                cumulative_time += CONFIG.multi_target.SHOT_DELAY
            cumulative_time += rotation_time

            # Calculate predicted position at engagement time
            predicted_pos = pigeon.position + pigeon.velocity * cumulative_time

            target_plan = TargetPlan(
                target=pigeon,
                engagement_time=self.current_time + cumulative_time,
                predicted_position=(
                    predicted_pos[0],
                    predicted_pos[1],
                    predicted_pos[2],
                ),
                rotation_time=rotation_time,
                confidence=0.8,
            )
            target_plans.append(target_plan)

        plan = EngagementPlan(
            targets=target_plans,
            total_time=cumulative_time,
            total_rotation=90.0,  # Mock value
            planning_time=0.001,
            algorithm_used="test",
        )

        # Verify predictions are different for each target
        positions = [tp.predicted_position for tp in plan.targets]

        # Each target should be predicted at a different time
        # Target 0: t=0.5s
        # Target 1: t=0.5+0.2+0.5=1.2s
        # Target 2: t=1.2+0.2+0.5=1.9s
        # Target 3: t=1.9+0.2+0.5=2.6s
        # Target 4: t=2.6+0.2+0.5=3.3s

        expected_times = [0.5, 1.2, 1.9, 2.6, 3.3]

        for i, (target_plan, expected_time) in enumerate(
            zip(plan.targets, expected_times)
        ):
            # Calculate expected position
            expected_pos = (
                self.pigeons[i].position + self.pigeons[i].velocity * expected_time
            )
            actual_pos = np.array(target_plan.predicted_position)

            # Verify positions match expected predictions
            np.testing.assert_array_almost_equal(
                actual_pos,
                expected_pos,
                decimal=2,
                err_msg=f"Target {i} prediction mismatch at t={expected_time}s",
            )

            print(f"Target {i + 1}: t={expected_time:.1f}s, pos={actual_pos}")

    def test_prediction_visualization_data(self):
        """Test that visualization gets correct cumulative timing data"""
        # Simulate the visualization calculation
        cumulative_time = 0.0
        visualization_data = []

        for i in range(5):
            # Calculate time to this target (cumulative)
            rotation_time = 0.5  # Mock rotation time
            cumulative_time += rotation_time
            if i > 0:
                cumulative_time += CONFIG.multi_target.SHOT_DELAY

            # Get current position and velocity
            current_pos = self.pigeons[i].position
            current_vel = self.pigeons[i].velocity

            # Calculate predicted position at engagement time
            predicted_pos = current_pos + current_vel * cumulative_time

            visualization_data.append(
                {
                    "target_id": i,
                    "cumulative_time": cumulative_time,
                    "predicted_position": predicted_pos,
                    "current_position": current_pos,
                }
            )

        # Verify cumulative times are increasing
        times = [data["cumulative_time"] for data in visualization_data]
        self.assertEqual(times, sorted(times), "Cumulative times should be increasing")

        # Verify each prediction is further along the trajectory
        for i in range(1, 5):
            prev_data = visualization_data[i - 1]
            curr_data = visualization_data[i]

            # Current target should be predicted further in time
            self.assertGreater(
                curr_data["cumulative_time"],
                prev_data["cumulative_time"],
                f"Target {i} should have greater cumulative time than target {i - 1}",
            )

            print(f"Target {i + 1}: t={curr_data['cumulative_time']:.3f}s")

    def test_edge_cases(self):
        """Test edge cases for multi-target prediction"""
        # Test with no targets
        empty_plan = EngagementPlan(
            targets=[],
            total_time=0,
            total_rotation=0,
            planning_time=0,
            algorithm_used="test",
        )
        self.assertEqual(len(empty_plan.targets), 0)

        # Test with single target (should work like normal targeting)
        single_target = [
            TargetPlan(
                target=self.pigeons[0],
                engagement_time=self.current_time + 0.5,
                predicted_position=(100, 50, 300),
                rotation_time=0.5,
                confidence=0.8,
            )
        ]
        single_plan = EngagementPlan(
            targets=single_target,
            total_time=0.5,
            total_rotation=30,
            planning_time=0.001,
            algorithm_used="test",
        )
        self.assertEqual(len(single_plan.targets), 1)


if __name__ == "__main__":
    # Run the test
    unittest.main(verbosity=2)
