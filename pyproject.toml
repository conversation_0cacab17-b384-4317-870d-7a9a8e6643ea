[project]
name = "pigeon-turret-simulator"
version = "0.1.0"
description = "Automated anti-pigeon turret simulation with 3D environment and targeting algorithms"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "pygame>=2.5.2",
    "numpy>=1.26.2",
    "opencv-python>=4.8.1",
    "pillow>=10.1.0",
    "loguru>=0.7.2",
    "python-dotenv>=1.0.0",
    "scipy>=1.11.4",
    "matplotlib>=3.8.2",
    "openai>=1.82.0",
    "rich>=14.0.0",
]
