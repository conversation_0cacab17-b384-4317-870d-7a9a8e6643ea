"""Camera control system with 360-degree view and click-to-point targeting"""
import numpy as np
from dataclasses import dataclass
from typing import <PERSON><PERSON>, Optional
from loguru import logger as log
import math

@dataclass
class CameraState:
    """Represents the current camera state"""
    yaw: float = 0.0  # Horizontal rotation (0-360)
    pitch: float = 0.0  # Vertical rotation (-90 to 90)
    fov: float = 90.0
    
    def normalize(self):
        """Normalize angles to valid ranges"""
        self.yaw = self.yaw % 360
        self.pitch = max(-89, min(89, self.pitch))

class Camera:
    """360-degree camera with smooth movement and click-to-point targeting"""
    
    def __init__(self, window_size: Tuple[int, int], fov: float = 90.0):
        self.window_width, self.window_height = window_size
        self.state = CameraState(fov=fov)
        self.target_state = CameraState(fov=fov)
        self.velocity = CameraState()
        self.max_speed = 180.0  # degrees per second
        self.acceleration = 360.0  # degrees per second^2
        
    def screen_to_angles(self, screen_pos: <PERSON><PERSON>[int, int]) -> <PERSON><PERSON>[float, float]:
        """Convert screen coordinates to yaw/pitch angles using lens projection"""
        x, y = screen_pos
        
        # Normalize to [-1, 1]
        norm_x = (2.0 * x / self.window_width) - 1.0
        norm_y = 1.0 - (2.0 * y / self.window_height)
        
        # Apply inverse lens distortion (simplified pinhole model)
        fov_rad = math.radians(self.state.fov)
        
        # Calculate angles from screen position
        # Horizontal angle relative to current view
        h_angle = math.atan(norm_x * math.tan(fov_rad / 2)) * 180 / math.pi
        
        # Vertical angle relative to current view
        aspect_ratio = self.window_width / self.window_height
        v_angle = math.atan(norm_y * math.tan(fov_rad / 2) / aspect_ratio) * 180 / math.pi
        
        # Convert to absolute angles
        target_yaw = self.state.yaw + h_angle
        target_pitch = self.state.pitch + v_angle
        
        return target_yaw % 360, max(-89, min(89, target_pitch))
    
    def angles_to_screen(self, yaw: float, pitch: float) -> Optional[Tuple[int, int]]:
        """Convert world angles to screen coordinates if visible"""
        # Calculate relative angles
        rel_yaw = (yaw - self.state.yaw + 180) % 360 - 180
        rel_pitch = pitch - self.state.pitch
        
        # Check if within FOV
        half_fov = self.state.fov / 2
        if abs(rel_yaw) > half_fov or abs(rel_pitch) > half_fov:
            return None
            
        # Project to screen
        fov_rad = math.radians(self.state.fov)
        
        # Normalize angles to [-1, 1] within FOV
        norm_x = math.tan(math.radians(rel_yaw)) / math.tan(fov_rad / 2)
        aspect_ratio = self.window_width / self.window_height
        norm_y = math.tan(math.radians(rel_pitch)) * aspect_ratio / math.tan(fov_rad / 2)
        
        # Convert to screen coordinates
        x = int((norm_x + 1.0) * self.window_width / 2)
        y = int((1.0 - norm_y) * self.window_height / 2)
        
        # Clamp to screen bounds
        x = max(0, min(self.window_width - 1, x))
        y = max(0, min(self.window_height - 1, y))
        
        return x, y
    
    def set_target_from_screen(self, screen_pos: Tuple[int, int]):
        """Set camera target from screen click position"""
        target_yaw, target_pitch = self.screen_to_angles(screen_pos)
        self.target_state.yaw = target_yaw
        self.target_state.pitch = target_pitch
        log.debug(f"Camera target set to yaw: {target_yaw:.1f}, pitch: {target_pitch:.1f}")
    
    def set_target_angles(self, yaw: float, pitch: float):
        """Directly set target angles"""
        self.target_state.yaw = yaw % 360
        self.target_state.pitch = max(-89, min(89, pitch))
    
    def update(self, dt: float):
        """Update camera position with smooth movement"""
        # Calculate angle differences (shortest path)
        yaw_diff = (self.target_state.yaw - self.state.yaw + 180) % 360 - 180
        pitch_diff = self.target_state.pitch - self.state.pitch
        
        # Apply acceleration-based movement
        if abs(yaw_diff) > 0.1:
            # Calculate desired velocity
            target_vel_yaw = np.sign(yaw_diff) * min(abs(yaw_diff) * 5, self.max_speed)
            
            # Apply acceleration
            vel_diff_yaw = target_vel_yaw - self.velocity.yaw
            accel_yaw = np.sign(vel_diff_yaw) * min(abs(vel_diff_yaw) / dt, self.acceleration)
            self.velocity.yaw += accel_yaw * dt
            
            # Clamp velocity
            self.velocity.yaw = np.sign(self.velocity.yaw) * min(abs(self.velocity.yaw), self.max_speed)
            
            # Update position
            self.state.yaw += self.velocity.yaw * dt
        else:
            self.velocity.yaw = 0
            self.state.yaw = self.target_state.yaw
            
        # Same for pitch
        if abs(pitch_diff) > 0.1:
            target_vel_pitch = np.sign(pitch_diff) * min(abs(pitch_diff) * 5, self.max_speed)
            vel_diff_pitch = target_vel_pitch - self.velocity.pitch
            accel_pitch = np.sign(vel_diff_pitch) * min(abs(vel_diff_pitch) / dt, self.acceleration)
            self.velocity.pitch += accel_pitch * dt
            self.velocity.pitch = np.sign(self.velocity.pitch) * min(abs(self.velocity.pitch), self.max_speed)
            self.state.pitch += self.velocity.pitch * dt
        else:
            self.velocity.pitch = 0
            self.state.pitch = self.target_state.pitch
            
        # Normalize angles
        self.state.normalize()
        
    def get_movement_time(self, target_yaw: float, target_pitch: float) -> float:
        """Calculate time to reach target position"""
        yaw_diff = abs((target_yaw - self.state.yaw + 180) % 360 - 180)
        pitch_diff = abs(target_pitch - self.state.pitch)
        
        # Simple approximation assuming constant acceleration
        max_diff = max(yaw_diff, pitch_diff)
        if max_diff < 0.1:
            return 0.0
            
        # Time = 2 * sqrt(distance / acceleration) for acceleration phase
        return 2 * math.sqrt(max_diff / self.acceleration)
    
    def get_direction_vector(self) -> np.ndarray:
        """Get the camera's forward direction as a 3D unit vector"""
        yaw_rad = math.radians(self.state.yaw)
        pitch_rad = math.radians(self.state.pitch)
        
        x = math.cos(pitch_rad) * math.sin(yaw_rad)
        y = math.sin(pitch_rad)
        z = math.cos(pitch_rad) * math.cos(yaw_rad)
        
        return np.array([x, y, z]) 