"""Targeting algorithms for automated turret control"""

import numpy as np
import math
from abc import ABC, abstractmethod
from typing import List, Optional, Tuple
from dataclasses import dataclass
from loguru import logger as log
import time
from enum import Enum

from entities import Pigeon, EntityManager
from camera import Camera
from multi_target_planner import MultiTargetPlanner, EngagementPlan


class PlanState(Enum):
    """State of multi-target plan execution"""

    IDLE = "idle"
    PLANNING = "planning"
    EXECUTING = "executing"
    COMPLETE = "complete"


@dataclass
class TargetingDecision:
    """Result of targeting algorithm"""

    target_pigeon: Optional[Pigeon]
    target_yaw: float
    target_pitch: float
    predicted_position: np.ndarray  # 3D position where we predict target will be
    confidence: float  # 0-1 score
    estimated_time_to_target: float
    algorithm_name: str


class TargetingAlgorithm(ABC):
    """Base class for targeting algorithms"""

    def __init__(self, name: str):
        self.name = name
        self.shots_fired = 0
        self.hits = 0

    @abstractmethod
    def select_target(
        self, pigeons: List[Pigeon], camera: Camera, entity_manager: EntityManager
    ) -> Optional[TargetingDecision]:
        """Select best target from available pigeons"""
        pass

    def update_stats(self, hit: bool):
        """Update algorithm statistics"""
        self.shots_fired += 1
        if hit:
            self.hits += 1

    @property
    def accuracy(self) -> float:
        """Get current accuracy"""
        return self.hits / self.shots_fired if self.shots_fired > 0 else 0.0


class NearestTargeting(TargetingAlgorithm):
    """Simple nearest target selection"""

    def __init__(self):
        super().__init__("Nearest")

    def select_target(
        self, pigeons: List[Pigeon], camera: Camera, entity_manager: EntityManager
    ) -> Optional[TargetingDecision]:
        if not pigeons:
            return None

        # Find nearest pigeon
        nearest_pigeon = None
        min_distance = float("inf")

        for pigeon in pigeons:
            if pigeon.active:
                distance = np.linalg.norm(pigeon.position)
                if distance < min_distance:
                    min_distance = distance
                    nearest_pigeon = pigeon

        if not nearest_pigeon:
            return None

        yaw, pitch, _ = nearest_pigeon.to_spherical()
        time_to_target = camera.get_movement_time(yaw, pitch)

        return TargetingDecision(
            target_pigeon=nearest_pigeon,
            target_yaw=yaw,
            target_pitch=pitch,
            predicted_position=nearest_pigeon.position.copy(),  # Use current position for nearest
            confidence=1.0
            / (1.0 + min_distance / 1000),  # Higher confidence for closer targets
            estimated_time_to_target=time_to_target,
            algorithm_name=self.name,
        )


class PredictiveTargeting(TargetingAlgorithm):
    """Predictive targeting with motion compensation"""

    def __init__(self):
        super().__init__("Predictive")

    def select_target(
        self, pigeons: List[Pigeon], camera: Camera, entity_manager: EntityManager
    ) -> Optional[TargetingDecision]:
        if not pigeons:
            return None

        best_score = -float("inf")
        best_decision = None

        # Sort pigeons by distance for prioritization
        sorted_pigeons = sorted(pigeons, key=lambda p: np.linalg.norm(p.position))

        for pigeon in sorted_pigeons:
            if not pigeon.active:
                continue

            # Calculate predicted position after turret movement
            yaw, pitch, distance = pigeon.to_spherical()

            # Skip targets that are too far
            if distance > 1200:  # MAX_ENGAGEMENT_DISTANCE
                continue

            movement_time = camera.get_movement_time(yaw, pitch)

            # Predict pigeon position after movement time
            future_pos = pigeon.position + pigeon.velocity * movement_time
            future_yaw, future_pitch, future_distance = self._to_spherical(future_pos)

            # Calculate projectile travel time
            projectile_time = future_distance / 500.0  # Projectile speed

            # Limit prediction time to avoid over-prediction
            total_time = min(movement_time + projectile_time, 2.0)

            # Final predicted position
            final_pos = pigeon.position + pigeon.velocity * total_time
            final_yaw, final_pitch, final_distance = self._to_spherical(final_pos)

            # Enhanced scoring system
            # 1. Distance score (heavily favor close targets)
            distance_score = 1.0 / (
                1.0 + final_distance / 400
            )  # More aggressive than before

            # 2. Speed penalty (slower targets are easier)
            speed = np.linalg.norm(pigeon.velocity)
            speed_score = 1.0 / (1.0 + speed / 80)  # Adjusted for new speed ranges

            # 3. Angular velocity penalty
            angular_vel = self._calculate_angular_velocity(pigeon, camera)
            velocity_score = 1.0 / (1.0 + angular_vel / 25)

            # 4. Time penalty (prefer quicker shots)
            time_score = 1.0 / (1.0 + movement_time)

            # 5. Shot history penalty (prefer fresh targets)
            shot_penalty = 1.0 / (1.0 + pigeon.shots_taken_at * 0.3)

            # Combined score with prioritization
            score = (
                distance_score
                * 3.0  # Heavy distance weight
                * speed_score
                * 1.5  # Moderate speed weight
                * velocity_score
                * 1.2  # Angular velocity weight
                * time_score
                * 1.0  # Time weight
                * shot_penalty
                * 1.5
            )  # Fresh target bonus

            if score > best_score:
                best_score = score
                best_decision = TargetingDecision(
                    target_pigeon=pigeon,
                    target_yaw=final_yaw,
                    target_pitch=final_pitch,
                    predicted_position=final_pos,  # Pass the predicted 3D position
                    confidence=min(score / 5.0, 1.0),  # Normalize confidence
                    estimated_time_to_target=movement_time,
                    algorithm_name=self.name,
                )

        return best_decision

    def _to_spherical(self, position: np.ndarray) -> Tuple[float, float, float]:
        """Convert 3D position to spherical coordinates"""
        x, y, z = position
        distance = np.linalg.norm(position)

        if distance < 0.001:
            return 0, 0, 0

        yaw = math.degrees(math.atan2(x, z)) % 360
        pitch = math.degrees(math.asin(np.clip(y / distance, -1, 1)))

        return yaw, pitch, distance

    def _calculate_angular_velocity(self, pigeon: Pigeon, camera: Camera) -> float:
        """Calculate angular velocity of pigeon relative to camera"""
        # Current angles
        yaw1, pitch1, dist1 = pigeon.to_spherical()

        # Future position (small time step)
        dt = 0.1
        future_pos = pigeon.position + pigeon.velocity * dt
        yaw2, pitch2, dist2 = self._to_spherical(future_pos)

        # Angular differences
        yaw_diff = abs((yaw2 - yaw1 + 180) % 360 - 180)
        pitch_diff = abs(pitch2 - pitch1)

        # Angular velocity in degrees per second
        angular_vel = math.sqrt(yaw_diff**2 + pitch_diff**2) / dt

        return angular_vel


class OpportunisticTargeting(TargetingAlgorithm):
    """Target pigeons that are easiest to hit right now"""

    def __init__(self):
        super().__init__("Opportunistic")

    def select_target(
        self, pigeons: List[Pigeon], camera: Camera, entity_manager: EntityManager
    ) -> Optional[TargetingDecision]:
        if not pigeons:
            return None

        best_score = -float("inf")
        best_decision = None

        for pigeon in pigeons:
            if not pigeon.active:
                continue

            yaw, pitch, distance = pigeon.to_spherical()

            # Skip targets that are too far
            if distance > 1000:  # Even stricter for opportunistic
                continue

            # Calculate how close pigeon is to current camera position
            angle_diff_yaw = abs((yaw - camera.state.yaw + 180) % 360 - 180)
            angle_diff_pitch = abs(pitch - camera.state.pitch)
            total_angle_diff = math.sqrt(angle_diff_yaw**2 + angle_diff_pitch**2)

            # Score based on minimal movement needed (heavily weighted)
            movement_score = 1.0 / (
                1.0 + total_angle_diff / 5
            )  # More sensitive to angle

            # Distance score (prefer very close targets)
            distance_score = 1.0 / (
                1.0 + distance / 300
            )  # More aggressive distance preference

            # Prefer slow-moving targets
            speed = np.linalg.norm(pigeon.velocity)
            speed_score = 1.0 / (1.0 + speed / 100)

            # Combined score with heavy weight on movement efficiency
            score = movement_score * 3.0 * distance_score * 2.0 * speed_score

            if score > best_score:
                best_score = score

                # For opportunistic, minimal lead compensation for close targets
                if distance < 500:
                    lead_time = distance / 500.0 * 0.3  # Less lead for close targets
                else:
                    lead_time = distance / 500.0 * 0.5

                future_pos = pigeon.position + pigeon.velocity * lead_time
                future_yaw, future_pitch, _ = self._to_spherical(future_pos)

                best_decision = TargetingDecision(
                    target_pigeon=pigeon,
                    target_yaw=future_yaw,
                    target_pitch=future_pitch,
                    predicted_position=future_pos,  # Pass the predicted 3D position
                    confidence=min(score, 1.0),
                    estimated_time_to_target=camera.get_movement_time(
                        future_yaw, future_pitch
                    ),
                    algorithm_name=self.name,
                )

        return best_decision

    def _to_spherical(self, position: np.ndarray) -> Tuple[float, float, float]:
        """Convert 3D position to spherical coordinates"""
        x, y, z = position
        distance = np.linalg.norm(position)

        if distance < 0.001:
            return 0, 0, 0

        yaw = math.degrees(math.atan2(x, z)) % 360
        pitch = math.degrees(math.asin(np.clip(y / distance, -1, 1)))

        return yaw, pitch, distance


class TargetingSystem:
    """Main targeting system that can switch between algorithms"""

    def __init__(self, initial_algorithm: str = "predictive"):
        self.algorithms = {
            "nearest": NearestTargeting(),
            "predictive": PredictiveTargeting(),
            "opportunistic": OpportunisticTargeting(),
        }
        self.current_algorithm = self.algorithms.get(
            initial_algorithm, self.algorithms["predictive"]
        )
        self.last_fire_time = 0.0
        self.current_target_id: Optional[int] = None
        self.target_lock_time: float = 0.0
        self.min_target_persistence: float = (
            1.5  # Stick with target for at least 1.5 seconds
        )
        self.shot_times: List[float] = []  # Track recent shot times for rate limiting

        # Multi-target support
        self.multi_target_planner = MultiTargetPlanner()
        self.current_engagement_plan: Optional[EngagementPlan] = None
        self.current_plan_index: int = 0
        self.multi_target_mode: bool = False

        # Multi-target KPI tracking
        self.multi_target_runs = 0
        self.multi_target_hits = 0
        self.current_run_hits = 0
        self.current_run_shots = 0

        # Shot/miss limiting
        self.total_shots_fired = 0
        self.total_misses = 0
        self.session_start_time = time.time()

        # Plan state management
        self.plan_state = PlanState.IDLE
        self.plan_start_time: Optional[float] = None
        self.last_plan_time: float = 0.0
        self.min_plan_interval: float = 2.0  # Minimum seconds between plans
        self.current_target_shot_time: Optional[float] = None
        self.shot_timeout: float = 2.0  # Max time to wait for shot result

    def can_continue_firing(self) -> bool:
        """Check if we can continue firing based on shot/miss limits"""
        from config import CONFIG

        # Check shot limit
        if CONFIG.multi_target.MAX_SHOTS_ALLOWED > 0:
            if self.total_shots_fired >= CONFIG.multi_target.MAX_SHOTS_ALLOWED:
                log.info(
                    f"🛑 Shot limit reached: {self.total_shots_fired}/{CONFIG.multi_target.MAX_SHOTS_ALLOWED}"
                )
                return False

        # Check miss limit
        if CONFIG.multi_target.MAX_MISSES_ALLOWED > 0:
            if self.total_misses >= CONFIG.multi_target.MAX_MISSES_ALLOWED:
                log.info(
                    f"🛑 Miss limit reached: {self.total_misses}/{CONFIG.multi_target.MAX_MISSES_ALLOWED}"
                )
                return False

        return True

    def on_miss_confirmed(self):
        """Called when a miss is confirmed"""
        self.total_misses += 1
        log.info(f"❌ Miss confirmed. Total misses: {self.total_misses}")

    def _should_create_new_plan(self, current_time: float) -> bool:
        """Check if we should create a new multi-target plan"""
        # Don't create new plan if we're still executing one
        if self.plan_state == PlanState.EXECUTING:
            # Check for timeout on current target
            if self.current_target_shot_time and (
                current_time - self.current_target_shot_time > self.shot_timeout
            ):
                log.info("⏱️ Shot timeout - moving to next target")
                self.current_plan_index += 1
                self.current_target_shot_time = None

                # Check if plan is complete
                if self.current_engagement_plan and self.current_plan_index >= len(
                    self.current_engagement_plan.targets
                ):
                    self.plan_state = PlanState.COMPLETE
                    log.info("✅ Multi-target plan complete")
            return False

        # Don't create new plan if we're currently planning
        if self.plan_state == PlanState.PLANNING:
            return False

        # Check minimum interval between plans
        if current_time - self.last_plan_time < self.min_plan_interval:
            return False

        # Create new plan if idle or complete
        return self.plan_state in [PlanState.IDLE, PlanState.COMPLETE]

    def select_target(
        self, entity_manager: EntityManager, camera: Camera, simulator=None
    ) -> Optional[TargetingDecision]:
        """Select target using current algorithm or multi-target plan"""
        from config import CONFIG

        # Check if we can continue firing
        if not self.can_continue_firing():
            return None

        visible_pigeons = entity_manager.get_visible_pigeons(camera)

        # Filter out pigeons that have reached shot limit or are fleeing
        eligible_pigeons = []
        for pigeon in visible_pigeons:
            if (
                pigeon.active
                and not pigeon.is_fleeing
                and entity_manager.get_shots_at_pigeon(pigeon.id)
                < CONFIG.targeting.MAX_SHOTS_PER_TARGET
            ):
                eligible_pigeons.append(pigeon)

        # Try multi-target planning if enabled
        if CONFIG.multi_target.MULTI_TARGET_ENABLED:
            current_time = time.time()

            # Check if we need a new plan
            if self._should_create_new_plan(current_time):
                # Set planning state
                self.plan_state = PlanState.PLANNING
                self.last_plan_time = current_time

                # Pause simulation if configured
                if simulator and CONFIG.multi_target.PAUSE_WHEN_CALCULATING:
                    simulator.multi_target_planning_pause = True
                    log.info("⏸️ Pausing simulation for multi-target planning...")

                plan = self.multi_target_planner.plan_engagement(
                    eligible_pigeons, camera.state.yaw, current_time
                )

                if simulator and CONFIG.multi_target.PAUSE_WHEN_CALCULATING:
                    simulator.multi_target_planning_pause = False
                    log.info("▶️ Resuming simulation after multi-target planning")

                if (
                    plan
                    and len(plan.targets) >= CONFIG.multi_target.MIN_TARGETS_FOR_MULTI
                ):
                    self.current_engagement_plan = plan
                    self.current_plan_index = 0
                    self.multi_target_mode = True
                    self.plan_state = PlanState.EXECUTING
                    self.plan_start_time = current_time
                    self.current_target_shot_time = None

                    # Start new multi-target run tracking
                    self.multi_target_runs += 1
                    self.current_run_hits = 0
                    self.current_run_shots = 0

                    log.info(
                        f"🎯 Multi-target run #{self.multi_target_runs}: {len(plan.targets)} targets, "
                        f"{plan.total_rotation:.1f}° rotation, {plan.algorithm_used} algorithm"
                    )
                else:
                    self.multi_target_mode = False
                    self.plan_state = PlanState.IDLE

            # Execute current plan if in multi-target mode
            if (
                self.multi_target_mode
                and self.current_engagement_plan
                and self.plan_state == PlanState.EXECUTING
            ):
                if self.current_plan_index < len(self.current_engagement_plan.targets):
                    target_plan = self.current_engagement_plan.targets[
                        self.current_plan_index
                    ]

                    # Check if target is still valid
                    if target_plan.target.active and not target_plan.target.is_fleeing:
                        # Use stored initial position and velocity from plan
                        if (
                            target_plan.initial_position
                            and target_plan.initial_velocity
                        ):
                            initial_pos = np.array(target_plan.initial_position)
                            initial_vel = np.array(target_plan.initial_velocity)
                        else:
                            # Fallback if not stored (shouldn't happen with updated planner)
                            elapsed_since_plan = current_time - self.plan_start_time
                            initial_pos = (
                                target_plan.target.position
                                - target_plan.target.velocity * elapsed_since_plan
                            )
                            initial_vel = target_plan.target.velocity

                        # Use the FIXED engagement time from the plan, not current time
                        # The prediction should be where the pigeon will be at the planned engagement time
                        time_to_engagement = (
                            target_plan.engagement_time - self.plan_start_time
                        )
                        predicted_position = (
                            initial_pos + initial_vel * time_to_engagement
                        )

                        # Convert to spherical coordinates
                        pred_x, pred_y, pred_z = predicted_position
                        distance = math.sqrt(pred_x**2 + pred_y**2 + pred_z**2)

                        if distance > 0.001:  # Avoid division by zero
                            yaw = math.degrees(math.atan2(pred_x, pred_z)) % 360
                            pitch = math.degrees(
                                math.asin(np.clip(pred_y / distance, -1, 1))
                            )
                        else:
                            yaw, pitch = 0, 0

                        # Track actual position for accuracy measurement
                        actual_pos = target_plan.target.position
                        prediction_error = np.linalg.norm(
                            actual_pos - predicted_position
                        )

                        # Update target plan with actual data
                        target_plan.actual_position = tuple(actual_pos)
                        target_plan.prediction_error = prediction_error

                        log.info(
                            f"🎯 Multi-target executing: target {self.current_plan_index + 1}/{len(self.current_engagement_plan.targets)}, "
                            f"pigeon {target_plan.target.id}, yaw={yaw:.1f}°, pitch={pitch:.1f}°, "
                            f"distance={distance:.0f}, prediction_error={prediction_error:.1f}"
                        )

                        return TargetingDecision(
                            target_pigeon=target_plan.target,
                            target_yaw=yaw,
                            target_pitch=pitch,
                            predicted_position=predicted_position,
                            confidence=target_plan.confidence,
                            estimated_time_to_target=target_plan.rotation_time,
                            algorithm_name=f"multi_{self.current_engagement_plan.algorithm_used}",
                        )
                    else:
                        # Target invalid, move to next
                        log.info(
                            f"❌ Multi-target: Target {target_plan.target.id} invalid, skipping"
                        )
                        self.current_plan_index += 1
                        self.current_target_shot_time = None

                        # Check if plan is complete
                        if self.current_plan_index >= len(
                            self.current_engagement_plan.targets
                        ):
                            self.plan_state = PlanState.COMPLETE
                            log.info(
                                "✅ Multi-target plan complete (all targets invalid)"
                            )

        # Fall back to single-target algorithm
        return self.current_algorithm.select_target(
            eligible_pigeons, camera, entity_manager
        )

    def switch_algorithm(self, algorithm_name: str):
        """Switch to different targeting algorithm"""
        if algorithm_name in self.algorithms:
            self.current_algorithm = self.algorithms[algorithm_name]
            log.info(f"Switched to {algorithm_name} targeting algorithm")

    def can_fire(self, current_time: float, cooldown: float) -> bool:
        """Check if turret can fire based on cooldown and rate limiting"""
        from config import CONFIG

        # Basic cooldown check
        if current_time - self.last_fire_time < cooldown:
            return False

        # Rate limiting check
        self._cleanup_old_shots(current_time)

        # Check if we're under the shots per second limit
        if len(self.shot_times) >= CONFIG.targeting.MAX_SHOTS_PER_SECOND:
            return False

        return True

    def _cleanup_old_shots(self, current_time: float):
        """Remove shot times older than 1 second"""
        self.shot_times = [t for t in self.shot_times if current_time - t < 1.0]

    def fire(self, current_time: float):
        """Record fire event"""
        self.last_fire_time = current_time
        self.shot_times.append(current_time)

        # Track shot time for multi-target mode
        if self.multi_target_mode and self.plan_state == PlanState.EXECUTING:
            self.current_target_shot_time = current_time
            log.debug(
                f"🔫 Fired at target {self.current_plan_index + 1}, tracking shot time"
            )

    def get_stats(self) -> dict:
        """Get statistics for all algorithms"""
        return {
            name: {
                "shots": algo.shots_fired,
                "hits": algo.hits,
                "accuracy": algo.accuracy,
            }
            for name, algo in self.algorithms.items()
        }

    def on_hit_confirmed(self):
        """Called when a hit is confirmed during multi-target mode"""
        if self.multi_target_mode:
            self.current_run_hits += 1
            self.multi_target_hits += 1
            log.info(
                f"🎯 Multi-target HIT! Run hits: {self.current_run_hits}, Total MT hits: {self.multi_target_hits}"
            )

            # Move to next target after hit
            if self.plan_state == PlanState.EXECUTING:
                self.current_plan_index += 1
                self.current_target_shot_time = None

                # Check if plan is complete
                if self.current_engagement_plan and self.current_plan_index >= len(
                    self.current_engagement_plan.targets
                ):
                    self.plan_state = PlanState.COMPLETE
                    self._analyze_plan_effectiveness()
                    log.info("✅ Multi-target plan complete after hit")

    def on_shot_fired(self):
        """Called when a shot is fired"""
        # Track total shots
        self.total_shots_fired += 1

        if self.multi_target_mode and self.current_engagement_plan:
            self.current_run_shots += 1

            # Don't advance plan index here - wait for hit/miss/timeout
            log.info(
                f"🔫 Multi-target shot {self.current_run_shots} at target {self.current_plan_index + 1}/"
                f"{len(self.current_engagement_plan.targets)} (hits so far: {self.current_run_hits}) "
                f"[Total shots: {self.total_shots_fired}]"
            )

    def _analyze_plan_effectiveness(self):
        """Analyze how well the multi-target plan performed"""
        if not self.current_engagement_plan:
            return

        plan = self.current_engagement_plan
        errors = [
            t.prediction_error for t in plan.targets if t.prediction_error is not None
        ]

        # Calculate KPIs
        run_accuracy = (
            (self.current_run_hits / self.current_run_shots * 100)
            if self.current_run_shots > 0
            else 0
        )
        overall_mt_accuracy = (
            (self.multi_target_hits / (self.multi_target_runs * 2) * 100)
            if self.multi_target_runs > 0
            else 0
        )  # Assuming 2 targets per run

        if errors:
            avg_error = sum(errors) / len(errors)
            max_error = max(errors)

            log.info(
                f"📊 Multi-target run #{self.multi_target_runs} complete: "
                f"{self.current_run_hits}/{self.current_run_shots} hits ({run_accuracy:.1f}%), "
                f"Overall MT accuracy: {overall_mt_accuracy:.1f}%, "
                f"avg prediction error: {avg_error:.1f}, max error: {max_error:.1f}, "
                f"algorithm: {plan.algorithm_used}"
            )
        else:
            log.info(
                f"📊 Multi-target run #{self.multi_target_runs} complete: "
                f"{self.current_run_hits}/{self.current_run_shots} hits ({run_accuracy:.1f}%), "
                f"Overall MT accuracy: {overall_mt_accuracy:.1f}%, "
                f"algorithm: {plan.algorithm_used}"
            )

    def get_current_multi_target_plan(self):
        """Get current multi-target plan for visualization"""
        if self.multi_target_mode and self.current_engagement_plan:
            return self.current_engagement_plan
        return None
