import os
import sys
from pathlib import Path

from loguru import logger as log
from rich.console import Console
from rich.traceback import install

# Enable rich tracebacks with locals
install(show_locals=False)
console = Console()


class CONFIG:
    LOG_LEVEL = "DEBUG"


# Configure Loguru
log.remove()  # Remove default handler
log.add(
    sys.stderr,
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
    level=CONFIG.LOG_LEVEL,
)

print("------ Testing Log Levels (if working you should see different colors for each level) ------")
# Test various log levels and formatting
log.trace("This is a TRACE level message - most verbose")
log.debug("This is a DEBUG level message - detailed info for debugging")
log.info("This is an INFO level message - general information")
log.success("This is a SUCCESS level message - operation completed successfully")
log.warning("This is a WARNING level message - something might be wrong")
log.error("This is an ERROR level message - something went wrong")
log.critical("This is a CRITICAL level message - system failure")

print("\n------ Testing Structured Logging (if working you should see key=value pairs after the message) ------")
# Test structured logging
log.info("User action", user_id=123, action="login", ip="***********")
log.debug("Performance metrics", fps=60, memory_usage="45MB", cpu_usage="12%")
log.warning("Resource threshold", current=85, threshold=80, resource="memory")

print("\n------ Testing Exception Logging (if working you should see a full stack trace with line numbers) ------")
# Test exception logging
try:
    result = 10 / 0
except ZeroDivisionError:
    log.exception("Division by zero error occurred")

print("\n------ Testing Contextual Logging (if working you should see 'component=targeting_system' in each log line) ------")
# Test with context
with log.contextualize(component="targeting_system"):
    log.info("Initializing targeting system")
    log.debug("Loading configuration parameters")
    log.success("Targeting system ready")

print("\n------ Testing Multiline Messages (if working you should see properly indented bullet points) ------")
# Test multiline messages
log.info("""
Multi-line log message:
- Component: Turret Simulator
- Status: Running
- Targets: 5 active
- Performance: Optimal
""")
