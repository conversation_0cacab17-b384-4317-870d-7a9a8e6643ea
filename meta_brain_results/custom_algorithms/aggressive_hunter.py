
# Custom Algorithm: Aggressive Close-Range Hunter
def select_target():
    if not pigeons:
        return None
    
    best_score = -float('inf')
    best_target = None
    
    # Focus only on very close targets
    close_pigeons = [p for p in pigeons if p['distance'] < 500 and not p['is_fleeing']]
    
    if not close_pigeons:
        # If no close targets, look for approaching targets
        for pigeon in pigeons:
            if pigeon['is_fleeing'] or pigeon['distance'] > 800:
                continue
            
            # Check if pigeon is approaching (negative radial velocity)
            pos = pigeon['position']
            vel = pigeon['velocity']
            radial_velocity = (pos[0]*vel[0] + pos[1]*vel[1] + pos[2]*vel[2]) / max(pigeon['distance'], 1)
            
            if radial_velocity < -20:  # Approaching at least 20 units/s
                close_pigeons.append(pigeon)
    
    for pigeon in close_pigeons:
        # Ultra-aggressive scoring for close targets
        distance_score = 100.0 / (1.0 + pigeon['distance'])
        
        # Prefer stationary targets
        speed_penalty = pigeon['speed'] / 100.0
        
        # Minimal angle consideration
        angle_diff = calculate_angle_diff(
            camera['yaw'], camera['pitch'],
            pigeon['yaw'], pigeon['pitch']
        )
        angle_score = 1.0 / (1.0 + angle_diff / 60)
        
        # Fresh target bonus
        freshness = 1.0 / (1.0 + pigeon['shots_taken'] * 0.2)
        
        score = distance_score * (1.0 - speed_penalty) * angle_score * freshness
        
        if score > best_score:
            best_score = score
            
            # Minimal lead for very close targets
            if pigeon['distance'] < 300:
                lead_time = 0.1  # Almost no lead
            else:
                lead_time = calculate_lead_time(pigeon['distance'], pigeon['speed']) * 0.4
            
            future_pos = [
                pigeon['position'][0] + pigeon['velocity'][0] * lead_time,
                pigeon['position'][1] + pigeon['velocity'][1] * lead_time,
                pigeon['position'][2] + pigeon['velocity'][2] * lead_time
            ]
            
            dist = max(np.linalg.norm(future_pos), 0.001)
            future_yaw = math.degrees(math.atan2(future_pos[0], future_pos[2])) % 360
            future_pitch = math.degrees(math.asin(np.clip(future_pos[1] / dist, -1, 1)))
            
            best_target = {
                'target_id': pigeon['id'],
                'target_yaw': future_yaw,
                'target_pitch': future_pitch,
                'confidence': min(score / 50.0, 1.0)
            }
    
    return best_target
