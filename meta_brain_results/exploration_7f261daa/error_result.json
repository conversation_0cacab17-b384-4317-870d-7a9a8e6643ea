{"plan_id": "exploration_7f261daa", "start_time": "2025-05-27T22:53:17.132926", "end_time": "2025-05-27T22:53:17.132929", "duration": 0.0, "unique_pigeons_hit": 0, "unique_pigeons_hit_per_minute": 0.0, "total_shots_fired": 0, "accuracy": 0.0, "pigeons_spawned": 0, "pigeons_escaped": 0, "average_reaction_time": 0.0, "parameter_stability": 0.0, "cpu_usage": 0.0, "memory_usage": 0.0, "error_count": 1, "warnings": ["cannot import name 'run_single_experiment' from 'headless_simulator' (consider renaming '/Users/<USER>/Documents/Code2/songs_with_words_with_friends/headless_simulator.py' if it has the same name as a library you intended to import)"], "raw_data_path": ""}