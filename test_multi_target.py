"""Test script for multi-target planner"""

import time
import math
import numpy as np
from loguru import logger as log
from config import CO<PERSON><PERSON>
from multi_target_planner import MultiTargetPlanner
from entities import Pigeon

# Initialize logger
CONFIG.log_init()


def create_test_pigeons():
    """Create test pigeons in various positions"""
    pigeons = []
    current_time = time.time()

    # Cluster of pigeons to the right
    for i in range(3):
        position = np.array([500 + i * 50, 100 + i * 20, 800], dtype=float)
        velocity = np.array([-50, 10, 0], dtype=float)
        p = Pigeon(
            position=position,
            velocity=velocity,
            size=30,
            active=True,
            id=i,
            spawn_time=current_time,
        )
        pigeons.append(p)

    # Single pigeon far left
    position = np.array([-600, 200, 700], dtype=float)
    velocity = np.array([100, -20, 0], dtype=float)
    p = Pigeon(
        position=position,
        velocity=velocity,
        size=30,
        active=True,
        id=3,
        spawn_time=current_time,
    )
    pigeons.append(p)

    # Close pigeon straight ahead
    position = np.array([50, 50, 400], dtype=float)
    velocity = np.array([0, 0, 0], dtype=float)
    p = Pigeon(
        position=position,
        velocity=velocity,
        size=30,
        active=True,
        id=4,
        spawn_time=current_time,
    )
    pigeons.append(p)

    return pigeons


def test_multi_target_planner():
    """Test the multi-target planner"""
    log.info("Testing Multi-Target Planner")

    # Create planner
    planner = MultiTargetPlanner()

    # Create test pigeons
    pigeons = create_test_pigeons()
    log.info(f"Created {len(pigeons)} test pigeons")

    # Test planning
    current_angle = 0.0  # Turret facing forward
    current_time = time.time()

    # Plan engagement
    plan = planner.plan_engagement(pigeons, current_angle, current_time)

    if plan:
        log.info(f"Generated plan with {len(plan.targets)} targets")
        log.info(f"Algorithm used: {plan.algorithm_used}")
        log.info(f"Total rotation: {plan.total_rotation:.1f}°")
        log.info(f"Total time: {plan.total_time:.2f}s")
        log.info(f"Planning time: {plan.planning_time * 1000:.1f}ms")

        for i, target_plan in enumerate(plan.targets):
            pigeon = target_plan.target
            pos = pigeon.position
            log.info(
                f"  Target {i + 1}: Pigeon {pigeon.id} at ({pos[0]:.0f}, {pos[1]:.0f}, {pos[2]:.0f})"
                f" -> ({target_plan.predicted_position[0]:.0f}, {target_plan.predicted_position[1]:.0f})"
                f" | Rotation: {target_plan.rotation_time:.2f}s | Confidence: {target_plan.confidence:.2f}"
            )
    else:
        log.warning("No plan generated")

    # Test with different configurations
    log.info("\nTesting different algorithms:")

    for algo in ["greedy", "angle_sort", "optimal"]:
        CONFIG.multi_target.PATH_ALGORITHM = algo
        planner = MultiTargetPlanner()

        start = time.perf_counter()
        plan = planner.plan_engagement(pigeons, current_angle, current_time)
        planning_time = (time.perf_counter() - start) * 1000

        if plan:
            log.info(
                f"{algo}: {len(plan.targets)} targets, "
                f"{plan.total_rotation:.1f}° rotation, "
                f"{planning_time:.1f}ms planning time"
            )

    # Test performance with many pigeons
    log.info("\nTesting performance scaling:")

    for n in [5, 10, 20, 50]:
        # Create n pigeons in random positions
        many_pigeons = []
        for i in range(n):
            angle = (i / n) * 360
            distance = 500 + (i % 5) * 200
            x = distance * math.sin(math.radians(angle))
            y = 100 + (i % 3) * 100
            z = distance * math.cos(math.radians(angle))

            position = np.array([x, y, z], dtype=float)
            velocity = np.array([-x / 10, 0, -z / 10], dtype=float)

            p = Pigeon(
                position=position,
                velocity=velocity,
                size=30,
                active=True,
                id=i,
                spawn_time=current_time,
            )
            many_pigeons.append(p)

        CONFIG.multi_target.PATH_ALGORITHM = "adaptive"
        planner = MultiTargetPlanner()

        start = time.perf_counter()
        plan = planner.plan_engagement(many_pigeons, current_angle, current_time)
        planning_time = (time.perf_counter() - start) * 1000

        if plan:
            log.info(
                f"{n} pigeons: Selected {len(plan.targets)}, "
                f"Algorithm: {plan.algorithm_used}, "
                f"Planning time: {planning_time:.1f}ms"
            )


if __name__ == "__main__":
    test_multi_target_planner()
