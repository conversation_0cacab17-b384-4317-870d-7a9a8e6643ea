# Multi-Target Turret System PRD

## Overview
Enhancement to the pigeon turret simulator to enable targeting and engaging multiple pigeons in a single planning cycle, optimizing the path to maximize hits while minimizing turret movement time.

## Problem Statement
Current system targets one pigeon at a time, missing opportunities to efficiently engage multiple targets when they're clustered or in favorable positions. This leads to:
- Inefficient turret movement patterns
- Missed opportunities when multiple targets are nearby
- Lower overall hit rate when facing swarms

## Goals
1. **Primary**: Increase overall hit rate by planning multi-target sequences
2. **Secondary**: Minimize turret movement time between shots
3. **Tertiary**: Maintain real-time performance (<10ms planning time)

## Technical Challenges & Solutions

### 1. Dynamic Path Planning
**Challenge**: Targets are moving while turret rotates between shots
**Solution**: 
- Predict target positions at shot time using velocity vectors
- Account for turret rotation time in predictions
- Use time-parameterized trajectories

### 2. Target Selection
**Challenge**: Choosing optimal subset from many targets
**Solution**: Multi-factor scoring system:
```
score = w1 * (1/distance) + w2 * threat_level + w3 * cluster_density + w4 * hit_probability
```
- Distance: Closer targets score higher
- Threat level: Targets moving toward center score higher
- Cluster density: Targets near other targets score higher
- Hit probability: Based on target speed and predictability

### 3. Path Optimization
**Challenge**: TSP-like problem but with moving targets and time constraints
**Solutions** (in order of implementation):
1. **Greedy Nearest Neighbor**: O(n²) - baseline algorithm
2. **Angle-Based Sorting**: O(n log n) - minimize rotation by sorting by angle
3. **Dynamic Programming**: O(n² * 2^n) - optimal for small n
4. **2-opt Improvement**: O(n²) - iterative improvement on greedy solution

### 4. Real-time Performance
**Challenge**: Must complete planning in <10ms
**Solution**:
- Hierarchical approach: start simple, refine if time allows
- Vectorized numpy operations
- Pre-computed lookup tables for common calculations
- Adaptive algorithm selection based on target count

## Implementation Design

### Core Components

```python
class MultiTargetPlanner:
    def __init__(self, turret_rotation_speed, shot_delay):
        self.turret_rotation_speed = turret_rotation_speed  # degrees/second
        self.shot_delay = shot_delay  # seconds between shots
        
    def plan_engagement(self, targets, current_turret_angle, max_targets=10):
        """Main planning method"""
        # 1. Score and select targets
        selected_targets = self.select_targets(targets, max_targets)
        
        # 2. Optimize firing sequence
        firing_sequence = self.optimize_path(selected_targets, current_turret_angle)
        
        # 3. Generate time-parameterized plan
        engagement_plan = self.generate_plan(firing_sequence, current_turret_angle)
        
        return engagement_plan
```

### Key Algorithms

#### Target Selection
```python
def select_targets(self, targets, max_targets):
    scores = []
    for target in targets:
        score = self.calculate_target_score(target)
        scores.append((score, target))
    
    # Sort by score and take top n
    scores.sort(reverse=True, key=lambda x: x[0])
    return [target for _, target in scores[:max_targets]]
```

#### Path Optimization (Greedy)
```python
def optimize_path_greedy(self, targets, start_angle):
    path = []
    current_angle = start_angle
    remaining = targets.copy()
    current_time = 0
    
    while remaining:
        # Find nearest target accounting for movement
        best_target = None
        best_time = float('inf')
        
        for target in remaining:
            # Predict where target will be
            rotation_time = self.calculate_rotation_time(current_angle, target.angle)
            future_pos = target.predict_position(current_time + rotation_time)
            total_time = rotation_time + self.shot_delay
            
            if total_time < best_time:
                best_time = total_time
                best_target = target
        
        path.append(best_target)
        remaining.remove(best_target)
        current_angle = best_target.angle
        current_time += best_time
    
    return path
```

## Configuration
```python
# config.py additions
MULTI_TARGET_MAX = 10  # Maximum targets per planning cycle
MULTI_TARGET_ENABLED = True
TURRET_ROTATION_SPEED = 180  # degrees/second
SHOT_DELAY = 0.1  # seconds between shots
PATH_PLANNING_TIMEOUT = 0.008  # 8ms max planning time
```

## Performance Metrics
- Planning time per cycle
- Average targets engaged per plan
- Hit rate improvement vs single-target
- Turret efficiency (degrees rotated per hit)

## Visualization
- Show planned path as dotted lines
- Number each target in sequence
- Display predicted hit positions
- Color code by confidence level

## Fallback Strategy
If planning takes too long or fails:
1. Revert to single-target mode
2. Use simple nearest-target selection
3. Log performance issue for analysis

## Testing Strategy
1. Unit tests for each algorithm component
2. Performance benchmarks for different target counts
3. Simulation tests comparing single vs multi-target
4. Edge cases: no targets, single target, max targets

## Success Criteria
- 20%+ improvement in overall hit rate
- Planning completes in <10ms for 95% of frames
- No degradation in single-target scenarios
- Smooth visual appearance (no jerky movements)

## Future Enhancements
1. Machine learning for weight optimization
2. Predictive clustering for swarm behavior
3. Cooperative targeting with multiple turrets
4. Priority target designation 