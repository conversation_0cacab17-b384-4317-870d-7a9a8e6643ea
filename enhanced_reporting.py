"""Enhanced Performance Reporting System"""

import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from loguru import logger as log
import numpy as np


@dataclass
class PerformanceTrend:
    """Performance metrics over time"""

    timestamp: float
    kpi_score: float
    accuracy: float
    reaction_time: float
    hit_rate: float
    shots_fired: int
    hits: int


@dataclass
class DistancePerformance:
    """Performance metrics by distance range"""

    range_name: str
    min_distance: float
    max_distance: float
    shots: int
    hits: int
    accuracy: float


@dataclass
class SpeedPerformance:
    """Performance metrics by target speed"""

    speed_range: str
    min_speed: float
    max_speed: float
    shots: int
    hits: int
    accuracy: float


@dataclass
class AIInsight:
    """AI-generated insight about performance"""

    category: str
    insight: str
    confidence: float
    recommendation: str


class EnhancedReporter:
    """Enhanced performance reporting with AI insights and trends"""

    def __init__(self):
        self.performance_timeline: List[PerformanceTrend] = []
        self.start_time = time.time()
        self.ai_insights: List[AIInsight] = []

    def record_performance_snapshot(self, evaluator, brain=None, entity_manager=None):
        """Record a performance snapshot for trend analysis"""
        try:
            stats = evaluator.get_current_stats()
            current_time = time.time()

            # Calculate KPI score if brain is available
            kpi_score = 0.0
            if (
                brain
                and hasattr(brain, "performance_snapshots")
                and brain.performance_snapshots
            ):
                latest_snapshot = brain.performance_snapshots[-1]
                kpi_score = latest_snapshot.current_params.get(
                    "composite_kpi_score", 0.0
                )

            # Get algorithm stats
            algo_stats = {}
            for algo_name, metrics in stats["algorithms"].items():
                algo_stats = metrics
                break  # Use first algorithm for now

            trend = PerformanceTrend(
                timestamp=current_time - self.start_time,
                kpi_score=kpi_score,
                accuracy=algo_stats.get("accuracy", 0.0),
                reaction_time=algo_stats.get("avg_reaction_time", 0.0),
                hit_rate=stats["session"]["total_pigeons_hit"]
                / max(stats["session"]["total_pigeons_spawned"], 1)
                * 100,
                shots_fired=algo_stats.get("shots_fired", 0),
                hits=algo_stats.get("hits", 0),
            )

            self.performance_timeline.append(trend)

        except Exception as e:
            log.error(f"Failed to record performance snapshot: {e}")

    def _analyze_distance_performance(self, evaluator) -> List[DistancePerformance]:
        """Analyze performance by distance ranges"""
        distance_ranges = [
            ("Close Range", 0, 500),
            ("Medium Range", 500, 800),
            ("Long Range", 800, float("inf")),
        ]

        results = []

        for range_name, min_dist, max_dist in distance_ranges:
            shots = 0
            hits = 0

            # Analyze shot history from all algorithms
            for algo_metrics in evaluator.algorithms.values():
                for shot in algo_metrics.shot_history:
                    if min_dist <= shot.distance < max_dist:
                        shots += 1
                        if shot.hit:
                            hits += 1

            accuracy = (hits / shots * 100) if shots > 0 else 0.0

            results.append(
                DistancePerformance(
                    range_name=range_name,
                    min_distance=min_dist,
                    max_distance=max_dist,
                    shots=shots,
                    hits=hits,
                    accuracy=accuracy,
                )
            )

        return results

    def _analyze_speed_performance(self, evaluator) -> List[SpeedPerformance]:
        """Analyze performance by target speed ranges"""
        speed_ranges = [
            ("Slow Targets", 0, 100),
            ("Medium Targets", 100, 200),
            ("Fast Targets", 200, float("inf")),
        ]

        results = []

        for range_name, min_speed, max_speed in speed_ranges:
            shots = 0
            hits = 0

            # Analyze shot history from all algorithms
            for algo_metrics in evaluator.algorithms.values():
                for shot in algo_metrics.shot_history:
                    if min_speed <= shot.target_speed < max_speed:
                        shots += 1
                        if shot.hit:
                            hits += 1

            accuracy = (hits / shots * 100) if shots > 0 else 0.0

            results.append(
                SpeedPerformance(
                    speed_range=range_name,
                    min_speed=min_speed,
                    max_speed=max_speed,
                    shots=shots,
                    hits=hits,
                    accuracy=accuracy,
                )
            )

        return results

    def _generate_ai_insights(
        self, evaluator, brain=None, entity_manager=None
    ) -> List[AIInsight]:
        """Generate AI insights based on performance data"""
        insights = []

        try:
            stats = evaluator.get_current_stats()
            distance_perf = self._analyze_distance_performance(evaluator)
            speed_perf = self._analyze_speed_performance(evaluator)

            # Insight 1: Distance performance analysis
            if len(distance_perf) >= 2:
                close_acc = distance_perf[0].accuracy
                long_acc = distance_perf[-1].accuracy
                if close_acc > long_acc + 10:
                    insights.append(
                        AIInsight(
                            category="Distance Analysis",
                            insight=f"Close-range targets show {close_acc:.1f}% accuracy vs {long_acc:.1f}% at long range",
                            confidence=0.9,
                            recommendation="Prioritize close-range targets for better hit rates",
                        )
                    )

            # Insight 2: Speed performance analysis
            if len(speed_perf) >= 2:
                slow_acc = speed_perf[0].accuracy
                fast_acc = speed_perf[-1].accuracy
                if slow_acc > fast_acc + 15:
                    insights.append(
                        AIInsight(
                            category="Speed Analysis",
                            insight=f"Slow targets have {slow_acc:.1f}% accuracy vs {fast_acc:.1f}% for fast targets",
                            confidence=0.85,
                            recommendation="Implement predictive targeting improvements for fast-moving targets",
                        )
                    )

            # Insight 3: Trend analysis
            if len(self.performance_timeline) >= 3:
                recent_kpi = np.mean(
                    [t.kpi_score for t in self.performance_timeline[-3:]]
                )
                early_kpi = np.mean(
                    [t.kpi_score for t in self.performance_timeline[:3]]
                )
                improvement = ((recent_kpi - early_kpi) / max(early_kpi, 0.01)) * 100

                if improvement > 10:
                    insights.append(
                        AIInsight(
                            category="Performance Trend",
                            insight=f"KPI score improved {improvement:.1f}% over session",
                            confidence=0.95,
                            recommendation="Continue current optimization strategy",
                        )
                    )

            # Insight 4: Brain optimization analysis
            if (
                brain
                and hasattr(brain, "optimization_history")
                and brain.optimization_history
            ):
                successful_opts = len(
                    [opt for opt in brain.optimization_history if opt.confidence > 0.7]
                )
                total_opts = len(brain.optimization_history)
                success_rate = (
                    (successful_opts / total_opts) * 100 if total_opts > 0 else 0
                )

                insights.append(
                    AIInsight(
                        category="AI Optimization",
                        insight=f"AI brain achieved {success_rate:.1f}% optimization success rate ({successful_opts}/{total_opts})",
                        confidence=0.8,
                        recommendation="AI optimization is performing well"
                        if success_rate > 60
                        else "Consider adjusting optimization parameters",
                    )
                )

        except Exception as e:
            log.error(f"Failed to generate AI insights: {e}")

        return insights

    def _format_time_trends(self) -> str:
        """Format performance trends over time"""
        if len(self.performance_timeline) < 2:
            return "Insufficient data for trend analysis"

        lines = ["=== Performance Trends ==="]

        # Divide timeline into segments
        total_time = self.performance_timeline[-1].timestamp
        segments = min(4, len(self.performance_timeline))
        segment_size = total_time / segments

        for i in range(segments):
            start_time = i * segment_size
            end_time = (i + 1) * segment_size

            # Get trends in this segment
            segment_trends = [
                t
                for t in self.performance_timeline
                if start_time <= t.timestamp < end_time
            ]

            if segment_trends:
                avg_kpi = np.mean([t.kpi_score for t in segment_trends])
                avg_acc = np.mean([t.accuracy for t in segment_trends])

                lines.append(
                    f"  {start_time:.0f}-{end_time:.0f}s: KPI {avg_kpi:.2f}, Accuracy {avg_acc:.1f}%"
                )

        # Overall trends
        if len(self.performance_timeline) >= 4:
            early_kpi = np.mean([t.kpi_score for t in self.performance_timeline[:2]])
            late_kpi = np.mean([t.kpi_score for t in self.performance_timeline[-2:]])
            kpi_change = late_kpi - early_kpi

            early_acc = np.mean([t.accuracy for t in self.performance_timeline[:2]])
            late_acc = np.mean([t.accuracy for t in self.performance_timeline[-2:]])
            acc_change = late_acc - early_acc

            lines.append("")
            lines.append(
                f"KPI Score: {'↗' if kpi_change > 0 else '↘'} {kpi_change:+.2f} change"
            )
            lines.append(
                f"Accuracy: {'↗' if acc_change > 0 else '↘'} {acc_change:+.1f}% change"
            )

        return "\n".join(lines)

    def _format_brain_analysis(self, brain=None) -> str:
        """Format AI brain performance analysis"""
        if not brain:
            return "=== AI Brain Analysis ===\nBrain not available"

        lines = ["=== AI Brain Optimization Summary ==="]

        # Brain status
        brain_enabled = hasattr(brain, "running") and brain.running
        lines.append(f"Brain Status: {'ENABLED' if brain_enabled else 'DISABLED'}")

        # Optimization history
        if hasattr(brain, "optimization_history") and brain.optimization_history:
            total_opts = len(brain.optimization_history)
            successful_opts = len(
                [opt for opt in brain.optimization_history if opt.confidence > 0.7]
            )
            lines.append(f"Total Optimizations: {total_opts}")
            lines.append(f"Successful Changes: {successful_opts}")

            # Latest optimization
            latest_opt = brain.optimization_history[-1]
            lines.append(f'Latest Reasoning: "{latest_opt.reasoning[:100]}..."')

        # Performance snapshots
        if hasattr(brain, "performance_snapshots") and brain.performance_snapshots:
            snapshots = brain.performance_snapshots
            if len(snapshots) >= 2:
                best_kpi = max(
                    s.current_params.get("composite_kpi_score", 0) for s in snapshots
                )
                current_kpi = snapshots[-1].current_params.get("composite_kpi_score", 0)
                lines.append(f"Best KPI Score: {best_kpi:.2f}")
                lines.append(f"Current KPI Score: {current_kpi:.2f}")

        # Experiment history
        if hasattr(brain, "experiment_history") and brain.experiment_history:
            experiments = brain.experiment_history
            lines.append(f"Experiments Conducted: {len(experiments)}")
            if experiments:
                best_exp = max(experiments, key=lambda x: x.kpi_score)
                lines.append(
                    f"Best Experiment KPI: {best_exp.kpi_score:.2f} (Accuracy: {best_exp.accuracy:.1f}%)"
                )

        return "\n".join(lines)

    def generate_enhanced_report(
        self, evaluator, brain=None, entity_manager=None
    ) -> str:
        """Generate comprehensive enhanced performance report"""
        try:
            # Record final snapshot
            self.record_performance_snapshot(evaluator, brain, entity_manager)

            # Get basic stats
            stats = evaluator.get_current_stats()
            session = stats["session"]

            # Generate insights
            self.ai_insights = self._generate_ai_insights(
                evaluator, brain, entity_manager
            )

            # Analyze performance by categories
            distance_perf = self._analyze_distance_performance(evaluator)
            speed_perf = self._analyze_speed_performance(evaluator)

            # Build report
            lines = []

            # Header
            lines.extend(
                ["=" * 60, "🎯 ENHANCED TURRET PERFORMANCE REPORT 🎯", "=" * 60, ""]
            )

            # Core metrics (enhanced)
            duration = session["session_duration"]
            hit_rate = (
                session["total_pigeons_hit"] / max(session["total_pigeons_spawned"], 1)
            ) * 100
            shots_per_hit = session["total_shots_fired"] / max(
                session["total_pigeons_hit"], 1
            )
            hits_per_minute = session["total_pigeons_hit"] / max(duration / 60, 1)

            lines.extend(
                [
                    "=== Core Performance Metrics ===",
                    f"Session Duration: {duration:.1f}s ({duration // 60:.0f}m {duration % 60:.0f}s)",
                    f"Total Pigeons Spawned: {session['total_pigeons_spawned']}",
                    f"Total Pigeons Hit: {session['total_pigeons_hit']} ({hit_rate:.1f}% hit rate)",
                    f"Total Shots Fired: {session['total_shots_fired']}",
                    f"Shots per Hit: {shots_per_hit:.2f}",
                    f"Hits per Minute: {hits_per_minute:.1f}",
                    "",
                ]
            )

            # Algorithm performance
            lines.append("=== Algorithm Performance Analysis ===")
            for algo_name, metrics in stats["algorithms"].items():
                lines.extend(
                    [
                        f"{algo_name} Algorithm:",
                        f"  Shots Fired: {metrics['shots_fired']}",
                        f"  Hits: {metrics['hits']}",
                        f"  Accuracy: {metrics['accuracy']:.1f}%",
                        f"  Avg Movement Time: {metrics['avg_movement_time']:.2f}s",
                        f"  Avg Reaction Time: {metrics['avg_reaction_time']:.2f}s",
                        "",
                    ]
                )

                # Distance performance breakdown
                if distance_perf:
                    lines.append("  Performance by Distance:")
                    for dist in distance_perf:
                        if dist.shots > 0:
                            lines.append(
                                f"  - {dist.range_name}: {dist.accuracy:.1f}% accuracy ({dist.shots} shots)"
                            )
                    lines.append("")

                # Speed performance breakdown
                if speed_perf:
                    lines.append("  Performance by Target Speed:")
                    for speed in speed_perf:
                        if speed.shots > 0:
                            lines.append(
                                f"  - {speed.speed_range}: {speed.accuracy:.1f}% accuracy ({speed.shots} shots)"
                            )
                    lines.append("")

            # Brain analysis
            lines.append(self._format_brain_analysis(brain))
            lines.append("")

            # Performance trends
            lines.append(self._format_time_trends())
            lines.append("")

            # AI Insights
            if self.ai_insights:
                lines.extend(["=== AI Analysis & Insights ===", ""])

                for insight in self.ai_insights:
                    confidence_stars = "★" * int(insight.confidence * 5)
                    lines.extend(
                        [
                            f"💡 {insight.category} {confidence_stars}",
                            f"   Finding: {insight.insight}",
                            f"   Recommendation: {insight.recommendation}",
                            "",
                        ]
                    )

            # Footer
            lines.extend(
                [
                    "=" * 60,
                    f"Report generated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    "=" * 60,
                ]
            )

            return "\n".join(lines)

        except Exception as e:
            log.error(f"Failed to generate enhanced report: {e}")
            return f"Error generating enhanced report: {e}"


def create_demo_data():
    """Create demo data for testing the enhanced reporting system"""
    from evaluation import PerformanceEvaluator, ShotMetrics, AlgorithmMetrics
    import random

    # Create evaluator with demo data
    evaluator = PerformanceEvaluator()

    # Simulate session metrics
    evaluator.session_metrics = {
        "total_pigeons_spawned": 877,
        "total_pigeons_hit": 47,
        "total_shots_fired": 125,
        "session_duration": 132.8,
    }

    # Create algorithm with shot history
    algo = AlgorithmMetrics("predictive")
    algo.shots_fired = 125
    algo.hits = 47
    algo.total_movement_time = 35.0
    algo.total_reaction_time = 155.0

    # Generate realistic shot history
    for i in range(125):
        hit = i < 47  # First 47 shots are hits
        distance = random.uniform(200, 1200)
        target_speed = random.uniform(50, 250)

        shot = ShotMetrics(
            shot_time=i * 1.06,  # Roughly every second
            target_id=random.randint(1, 200),
            algorithm="predictive",
            movement_time=random.uniform(0.1, 0.8),
            projectile_flight_time=distance / 500,
            hit=hit,
            distance=distance,
            target_speed=target_speed,
            angular_velocity=random.uniform(0, 45),
        )
        algo.shot_history.append(shot)

    evaluator.algorithms["predictive"] = algo

    # Create mock brain with optimization history
    class MockBrain:
        def __init__(self):
            self.running = True
            self.optimization_history = []
            self.performance_snapshots = []
            self.experiment_history = []

            # Add some mock optimizations
            from brain import OptimizationResult

            self.optimization_history.append(
                OptimizationResult(
                    reasoning="Reduced fire cooldown from 0.3s to 0.25s improved close-range accuracy by 12%",
                    tool="parameter_adjustment",
                    conclusion="Fire cooldown optimization successful",
                    parameter_changes={"fire_cooldown": -0.05},
                    confidence=0.85,
                )
            )

            self.optimization_history.append(
                OptimizationResult(
                    reasoning="Targeting system shows better performance with angle threshold of 5.2° vs 8.0°",
                    tool="targeting_optimization",
                    conclusion="Angle threshold reduced for better precision",
                    parameter_changes={"angle_threshold": -2.8},
                    confidence=0.92,
                )
            )

            # Add mock performance snapshots
            from brain import PerformanceSnapshot

            for i in range(5):
                snapshot = PerformanceSnapshot(
                    timestamp=f"2025-05-27T22:{40 + i * 2}:00",
                    accuracy=30.0 + i * 3.5,
                    shots_fired=25 * (i + 1),
                    hits=int((30.0 + i * 3.5) * 25 * (i + 1) / 100),
                    avg_movement_time=0.35 - i * 0.02,
                    avg_reaction_time=1.5 - i * 0.1,
                    pigeons_in_range=8 + random.randint(-2, 3),
                    total_pigeons=150 + i * 30,
                    current_params={"composite_kpi_score": 0.4 + i * 0.1},
                )
                self.performance_snapshots.append(snapshot)

    return evaluator, MockBrain()


def demo_enhanced_reporting():
    """Demo function to test the enhanced reporting system"""
    print("🎯 Enhanced Reporting System Demo")
    print("=" * 50)

    # Create demo data
    evaluator, mock_brain = create_demo_data()

    # Create enhanced reporter
    reporter = EnhancedReporter()

    # Simulate some performance snapshots over time
    for i in range(6):
        reporter.record_performance_snapshot(evaluator, mock_brain)
        # Simulate time progression
        reporter.performance_timeline[-1].timestamp = i * 22.0  # Every 22 seconds
        reporter.performance_timeline[-1].kpi_score = 0.4 + i * 0.08
        reporter.performance_timeline[-1].accuracy = 30.0 + i * 3.0

    # Generate and display the enhanced report
    report = reporter.generate_enhanced_report(evaluator, mock_brain)
    print(report)

    # Save report to file
    with open("enhanced_report_demo.txt", "w") as f:
        f.write(report)

    print(f"\n📄 Report saved to: enhanced_report_demo.txt")
    return reporter


if __name__ == "__main__":
    demo_enhanced_reporting()
