"""Test trajectory-based planning effectiveness"""

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import numpy as np
from loguru import logger as log
from config import CONFIG
from multi_target_planner import MultiTargetPlanner
from entities import Pigeon
import math

# Initialize logger
CONFIG.environment.LOG_LEVEL = "INFO"
CONFIG.log_init()


def create_moving_cluster(start_x, start_z, velocity, num_pigeons=5):
    """Create a cluster of pigeons moving together"""
    pigeons = []
    current_time = time.time()

    for i in range(num_pigeons):
        # Slight position variation
        position = np.array(
            [
                start_x + i * 30,  # Spread horizontally
                100 + i * 10,  # Slight height variation
                start_z + i * 20,  # Slight depth variation
            ],
            dtype=float,
        )

        # Similar velocity with small variations
        vel_variation = np.random.uniform(-10, 10, 3)
        pigeon_velocity = velocity + vel_variation

        pigeon = <PERSON>eon(
            position=position,
            velocity=pigeon_velocity,
            size=30,
            active=True,
            id=i,
            spawn_time=current_time,
        )
        pigeons.append(pigeon)

    return pigeons


def simulate_plan_execution(plan, start_angle=0.0):
    """Simulate executing a plan and measure spread"""
    current_angle = start_angle
    current_time = 0.0
    positions_at_engagement = []

    for i, target_plan in enumerate(plan.targets):
        # Simulate rotation time
        rotation_time = target_plan.rotation_time
        current_time += rotation_time + CONFIG.multi_target.SHOT_DELAY

        # Calculate actual position at engagement
        actual_pos = (
            target_plan.target.position + target_plan.target.velocity * current_time
        )
        positions_at_engagement.append(actual_pos)

        # Update angle for next target
        current_angle = math.degrees(math.atan2(actual_pos[0], actual_pos[2]))

    # Calculate spread metrics
    if len(positions_at_engagement) > 1:
        # Average distance between consecutive targets
        distances = []
        for i in range(1, len(positions_at_engagement)):
            dist = np.linalg.norm(
                positions_at_engagement[i] - positions_at_engagement[i - 1]
            )
            distances.append(dist)

        avg_distance = np.mean(distances)
        max_distance = np.max(distances)

        # Total spread (distance from first to last)
        total_spread = np.linalg.norm(
            positions_at_engagement[-1] - positions_at_engagement[0]
        )

        return {
            "avg_distance": avg_distance,
            "max_distance": max_distance,
            "total_spread": total_spread,
            "positions": positions_at_engagement,
        }

    return None


# Test scenarios
log.info("=== Testing Trajectory-Based Planning ===")

# Scenario 1: Fast-moving cluster going left
log.info("\nScenario 1: Fast cluster moving left")
pigeons = create_moving_cluster(500, 800, np.array([-150, 0, 0]), 5)
planner = MultiTargetPlanner()

# Plan engagement
plan = planner.plan_engagement(pigeons, 0.0, 0.0)
if plan:
    log.info(
        f"Plan created: {len(plan.targets)} targets, {plan.total_rotation:.1f}° rotation"
    )

    # Simulate execution
    metrics = simulate_plan_execution(plan)
    if metrics:
        log.info(f"Execution metrics:")
        log.info(f"  - Average distance between targets: {metrics['avg_distance']:.1f}")
        log.info(f"  - Maximum distance between targets: {metrics['max_distance']:.1f}")
        log.info(f"  - Total spread (first to last): {metrics['total_spread']:.1f}")

# Scenario 2: Diverging targets
log.info("\nScenario 2: Diverging targets")
diverging_pigeons = []
for i in range(5):
    angle = i * 20 - 40  # -40 to +40 degrees
    velocity = np.array(
        [100 * math.sin(math.radians(angle)), 0, 100 * math.cos(math.radians(angle))]
    )
    position = np.array([300 + i * 50, 100, 700], dtype=float)

    pigeon = Pigeon(
        position=position,
        velocity=velocity,
        size=30,
        active=True,
        id=i + 10,
        spawn_time=time.time(),
    )
    diverging_pigeons.append(pigeon)

plan2 = planner.plan_engagement(diverging_pigeons, 0.0, 0.0)
if plan2:
    log.info(
        f"Plan created: {len(plan2.targets)} targets, {plan2.total_rotation:.1f}° rotation"
    )

    metrics2 = simulate_plan_execution(plan2)
    if metrics2:
        log.info(f"Execution metrics:")
        log.info(
            f"  - Average distance between targets: {metrics2['avg_distance']:.1f}"
        )
        log.info(
            f"  - Maximum distance between targets: {metrics2['max_distance']:.1f}"
        )
        log.info(f"  - Total spread (first to last): {metrics2['total_spread']:.1f}")

# Scenario 3: Converging targets (should be prioritized)
log.info("\nScenario 3: Converging targets")
converging_pigeons = []
for i in range(5):
    # Start positions in a wide arc
    angle = i * 40 - 80  # -80 to +80 degrees
    start_x = 800 * math.sin(math.radians(angle))
    start_z = 800 * math.cos(math.radians(angle))

    # All moving toward center
    velocity = np.array([-start_x / 10, 0, -start_z / 10])
    position = np.array([start_x, 100, start_z], dtype=float)

    pigeon = Pigeon(
        position=position,
        velocity=velocity,
        size=30,
        active=True,
        id=i + 20,
        spawn_time=time.time(),
    )
    converging_pigeons.append(pigeon)

plan3 = planner.plan_engagement(converging_pigeons, 0.0, 0.0)
if plan3:
    log.info(
        f"Plan created: {len(plan3.targets)} targets, {plan3.total_rotation:.1f}° rotation"
    )

    metrics3 = simulate_plan_execution(plan3)
    if metrics3:
        log.info(f"Execution metrics:")
        log.info(
            f"  - Average distance between targets: {metrics3['avg_distance']:.1f}"
        )
        log.info(
            f"  - Maximum distance between targets: {metrics3['max_distance']:.1f}"
        )
        log.info(f"  - Total spread (first to last): {metrics3['total_spread']:.1f}")

log.info("\n=== Test Complete ===")
