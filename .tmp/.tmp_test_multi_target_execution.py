"""Test multi-target execution and logging"""

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
from loguru import logger as log
from config import CONFIG
from targeting import TargetingSystem
from entities import Pigeon, <PERSON><PERSON><PERSON><PERSON>ana<PERSON>
from camera import Camera
import numpy as np

# Initialize logger with DEBUG level
CONFIG.environment.LOG_LEVEL = "DEBUG"
CONFIG.log_init()

# Create test components
entity_manager = EntityManager()
camera = Camera((1920, 1080), 130.0)
targeting_system = TargetingSystem("predictive")

# Create a cluster of test pigeons
current_time = time.time()
for i in range(5):
    position = np.array([400 + i * 50, 100, 800], dtype=float)
    velocity = np.array([-50, 10, 0], dtype=float)
    pigeon = Pigeon(
        position=position,
        velocity=velocity,
        size=30,
        active=True,
        id=i,
        spawn_time=current_time,
    )
    entity_manager.pigeons.append(pigeon)

log.info(f"Created {len(entity_manager.pigeons)} test pigeons")

# Test multi-target planning
for i in range(10):
    decision = targeting_system.select_target(entity_manager, camera)

    if decision:
        log.info(
            f"Decision {i}: Algorithm={decision.algorithm_name}, Target={decision.target_pigeon.id}"
        )

        # Check if we're in multi-target mode
        plan = targeting_system.get_current_multi_target_plan()
        if plan:
            log.info(
                f"Multi-target plan active: {len(plan.targets)} targets, algorithm={plan.algorithm_used}"
            )
            for j, target_plan in enumerate(plan.targets):
                log.info(f"  Target {j + 1}: Pigeon {target_plan.target.id}")

        # Simulate shot fired
        targeting_system.on_shot_fired()
        time.sleep(0.1)
    else:
        log.info(f"Decision {i}: No target")

log.info("Test complete")
