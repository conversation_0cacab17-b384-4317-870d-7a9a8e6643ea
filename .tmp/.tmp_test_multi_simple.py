"""Test multi-target with simple scenario"""

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import numpy as np
from loguru import logger as log
from config import CONFIG
from targeting import TargetingSystem
from entities import Pigeon, <PERSON><PERSON><PERSON><PERSON>ana<PERSON>
from camera import Camera

# Initialize with debug logging
CONFIG.environment.LOG_LEVEL = "DEBUG"
CONFIG.log_init()

# Create components
entity_manager = EntityManager()
camera = Camera((1920, 1080), 130.0)
targeting_system = TargetingSystem("predictive")

# Create 3 stationary pigeons in a line
pigeons = []
for i in range(3):
    pigeon = Pigeon(
        position=np.array([100 + i * 100, 50, 500], dtype=float),  # Spread horizontally
        velocity=np.array([0, 0, 0], dtype=float),  # Stationary for simplicity
        size=30,
        active=True,
        id=i,
        spawn_time=0.0,
    )
    entity_manager.pigeons.append(pigeon)
    pigeons.append(pigeon)

log.info("Created 3 stationary pigeons")
log.info(f"Pigeon 0: position={pigeons[0].position}")
log.info(f"Pigeon 1: position={pigeons[1].position}")
log.info(f"Pigeon 2: position={pigeons[2].position}")

# Test multi-target planning
log.info("\nTesting multi-target planning...")

# First decision should create a plan
decision1 = targeting_system.select_target(entity_manager, camera)
if decision1:
    log.info(
        f"Decision 1: Algorithm={decision1.algorithm_name}, Target={decision1.target_pigeon.id}"
    )

    plan = targeting_system.get_current_multi_target_plan()
    if plan:
        log.info(f"Multi-target plan created with {len(plan.targets)} targets")
        for i, tp in enumerate(plan.targets):
            log.info(
                f"  Target {i}: Pigeon {tp.target.id}, predicted_pos={tp.predicted_position}"
            )

# Simulate firing
targeting_system.on_shot_fired()
time.sleep(0.1)

# Second decision should move to next target
decision2 = targeting_system.select_target(entity_manager, camera)
if decision2:
    log.info(
        f"\nDecision 2: Algorithm={decision2.algorithm_name}, Target={decision2.target_pigeon.id}"
    )

log.info("\nTest complete")
