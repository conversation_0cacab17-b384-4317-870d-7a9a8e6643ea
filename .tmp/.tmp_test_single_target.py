"""Test single-target mode"""

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import numpy as np
from loguru import logger as log
from config import CONFIG
from targeting import TargetingSystem
from entities import Pigeon, <PERSON><PERSON><PERSON><PERSON>anager
from camera import Camera

# Initialize
CONFIG.log_init()
CONFIG.multi_target.MULTI_TARGET_ENABLED = False  # Ensure single-target mode

# Create components
entity_manager = EntityManager()
camera = Camera((1920, 1080), 130.0)
targeting_system = TargetingSystem("predictive")

# Create a simple test pigeon
pigeon = Pigeon(
    position=np.array([100, 50, 500], dtype=float),
    velocity=np.array([-20, 0, 0], dtype=float),
    size=30,
    active=True,
    id=1,
    spawn_time=0.0,
)
entity_manager.pigeons.append(pigeon)

log.info("Testing single-target mode...")

# Test targeting
for i in range(5):
    decision = targeting_system.select_target(entity_manager, camera)

    if decision:
        log.info(f"Decision {i}:")
        log.info(f"  Algorithm: {decision.algorithm_name}")
        log.info(f"  Target: Pigeon {decision.target_pigeon.id}")
        log.info(
            f"  Target angles: yaw={decision.target_yaw:.1f}, pitch={decision.target_pitch:.1f}"
        )
        log.info(f"  Confidence: {decision.confidence:.3f}")
        log.info(f"  Distance: {np.linalg.norm(decision.target_pigeon.position):.1f}")
    else:
        log.warning(f"Decision {i}: No target selected")

    # Simulate time passing
    pigeon.update(0.1)
    time.sleep(0.1)

log.info("Test complete")
