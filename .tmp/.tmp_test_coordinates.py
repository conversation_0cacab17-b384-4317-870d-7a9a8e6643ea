"""Test coordinate system and basic targeting"""

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
from loguru import logger as log
from config import CONFIG
from entities import Pigeon
import math

# Initialize logger
CONFIG.log_init()

# Test coordinate system
log.info("=== Testing Coordinate System ===")

# Create a pigeon at known position
position = np.array([100, 50, 500], dtype=float)  # x=right, y=up, z=forward
velocity = np.array([0, 0, 0], dtype=float)  # stationary

pigeon = Pigeon(
    position=position, velocity=velocity, size=30, active=True, id=1, spawn_time=0.0
)

# Test spherical conversion
yaw, pitch, distance = pigeon.to_spherical()
log.info(f"Position: x={position[0]}, y={position[1]}, z={position[2]}")
log.info(f"Spherical: yaw={yaw:.1f}°, pitch={pitch:.1f}°, distance={distance:.1f}")

# Expected values:
# yaw = atan2(x, z) = atan2(100, 500) = ~11.3°
# pitch = asin(y / distance) = asin(50 / 509.9) = ~5.6°
# distance = sqrt(100² + 50² + 500²) = ~509.9

expected_yaw = math.degrees(math.atan2(100, 500)) % 360
expected_pitch = math.degrees(math.asin(50 / math.sqrt(100**2 + 50**2 + 500**2)))
expected_distance = math.sqrt(100**2 + 50**2 + 500**2)

log.info(
    f"Expected: yaw={expected_yaw:.1f}°, pitch={expected_pitch:.1f}°, distance={expected_distance:.1f}"
)

# Test movement
log.info("\n=== Testing Movement ===")
moving_pigeon = Pigeon(
    position=np.array([0, 0, 1000], dtype=float),
    velocity=np.array([100, 0, -100], dtype=float),  # Moving right and toward us
    size=30,
    active=True,
    id=2,
    spawn_time=0.0,
)

log.info(f"Initial position: {moving_pigeon.position}")
log.info(f"Velocity: {moving_pigeon.velocity}")

# Simulate 1 second
moving_pigeon.update(1.0)
log.info(f"After 1s: {moving_pigeon.position}")

# Test prediction
log.info("\n=== Testing Prediction ===")
from multi_target_planner import MultiTargetPlanner

planner = MultiTargetPlanner()

# Create a target moving left
target = Pigeon(
    position=np.array([200, 100, 800], dtype=float),
    velocity=np.array([-50, 0, 0], dtype=float),
    size=30,
    active=True,
    id=3,
    spawn_time=0.0,
)

# Test scoring with prediction
score = planner._score_target(target, 0.0, [target], 0.0, 0.0)
log.info(f"Target score: {score:.3f}")

# Check predicted position calculation
rotation_time = 0.5  # assume 0.5s to rotate
future_pos = target.position + target.velocity * rotation_time
log.info(f"Current pos: {target.position}")
log.info(f"Predicted pos after {rotation_time}s: {future_pos}")
log.info(f"Movement: {future_pos - target.position}")
