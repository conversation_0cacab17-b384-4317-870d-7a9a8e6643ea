# Custom Algorithm Execution for Meta-Brain

## Overview

The meta-brain now has the capability to write and execute custom targeting algorithms in Python. These algorithms run within the constraints of the brain's available controls, ensuring they work within the existing simulation framework.

## Architecture

### 1. **CustomAlgorithmExecutor** (`custom_algorithm_executor.py`)
- Executes custom targeting algorithms written by the meta-brain
- Provides a sandboxed execution environment with restricted builtins
- Converts pigeon and camera data into a safe format for algorithm execution
- Validates algorithm output and converts it to standard targeting decisions

### 2. **Meta-Brain Integration** (`meta_brain.py`)
- `StrategyPlanner._generate_custom_algorithm_plan()`: Creates experiment plans for custom algorithms
- `StrategyPlanner._generate_custom_algorithm()`: Generates custom algorithm code (placeholder for AI generation)
- `StrategyPlanner._get_algorithm_template_variation()`: Provides algorithm variations for testing

### 3. **Headless Simulator Support** (`headless_simulator.py`)
- Added `_setup_custom_algorithm()`: Loads custom algorithms from files
- Modified `_get_targeting_decision()`: Executes custom algorithms when available
- Falls back to standard targeting system if custom algorithm fails

## Available Controls

Custom algorithms have access to:

### Data Structures
- **pigeons**: List of pigeon data with keys:
  - `id`: Unique identifier
  - `position`: 3D position array
  - `velocity`: 3D velocity array
  - `distance`: Distance from turret
  - `yaw`, `pitch`: Angular position
  - `speed`: Scalar speed
  - `shots_taken`: Number of shots taken at this pigeon
  - `is_fleeing`: Whether pigeon is fleeing

- **camera**: Current camera state:
  - `yaw`, `pitch`: Current camera angles
  - `fov`: Field of view
  - `max_speed`: Maximum rotation speed

### Helper Functions
- `calculate_angle_diff(yaw1, pitch1, yaw2, pitch2)`: Angular difference between two positions
- `calculate_movement_time(yaw, pitch)`: Time for turret to aim at position
- `calculate_lead_time(distance, speed)`: Projectile travel time

### Libraries
- `math`: Standard math functions
- `np` (numpy): Array operations

### Constants
- `MAX_ENGAGEMENT_DISTANCE`: 1200 units
- `PROJECTILE_SPEED`: 500 units/s
- `ANGLE_THRESHOLD_CLOSE`: 3.0 degrees
- `ANGLE_THRESHOLD_FAR`: 8.0 degrees

## Algorithm Template

```python
def select_target():
    """Select best target from available pigeons
    
    Returns:
        dict with keys: target_id, target_yaw, target_pitch, confidence
        or None if no suitable target
    """
    if not pigeons:
        return None
    
    # Custom targeting logic here
    
    return {
        'target_id': selected_pigeon['id'],
        'target_yaw': calculated_yaw,
        'target_pitch': calculated_pitch,
        'confidence': confidence_score  # 0.0 to 1.0
    }
```

## Example Custom Algorithms

### 1. Aggressive Close-Range Hunter
Focuses on very close targets (<500 units) or approaching targets. Uses minimal lead time for close targets.

### 2. Predictive Interception
Calculates precise interception points considering turret movement time and projectile travel time.

## Experiment Integration

Custom algorithms are integrated into the meta-brain's experiment system:

1. Algorithm code is generated (or selected from templates)
2. Code is saved to `meta_brain_results/custom_algorithms/`
3. Experiment plan includes path to algorithm file
4. Headless simulator loads and executes the algorithm
5. Results are compared against standard algorithms
6. Best-performing algorithms are saved for future use

## Safety Features

- Restricted execution environment (limited builtins)
- Input validation for algorithm results
- Fallback to standard targeting if custom algorithm fails
- Error logging for debugging
- Timeout protection (via standard Python execution)

## Future Enhancements

1. **AI-Generated Algorithms**: Use GPT-4 to generate novel targeting strategies based on performance data
2. **Algorithm Evolution**: Genetic programming to evolve successful algorithms
3. **Multi-Algorithm Ensemble**: Combine multiple algorithms for different scenarios
4. **Performance Profiling**: Track algorithm execution time and efficiency
5. **Visual Debugging**: Generate visualizations of algorithm decisions

## Usage Example

```python
from custom_algorithm_executor import CustomAlgorithmExecutor
from headless_simulator import run_single_experiment

# Create and save a custom algorithm
algorithm_code = """
def select_target():
    # Custom logic here
    pass
"""

# Run experiment with custom algorithm
config = {
    "custom_algorithm": "path/to/algorithm.py",
    "fire_cooldown": 0.2,
    # ... other parameters
}

result = run_single_experiment(config, duration=30.0)
```

This system enables the meta-brain to explore novel targeting strategies beyond the pre-programmed algorithms, potentially discovering more effective approaches through experimentation. 