"""Test custom algorithm execution in the meta-brain framework"""

from pathlib import Path
from loguru import logger as log

from headless_simulator import run_single_experiment

# Configure logging
log.remove()
log.add(lambda msg: print(msg), level="INFO")


def test_custom_algorithm():
    """Test custom algorithm generation and execution"""

    # Create a custom algorithm
    custom_algorithm = """
# Custom Algorithm: Aggressive Close-Range Hunter
def select_target():
    if not pigeons:
        return None
    
    best_score = -float('inf')
    best_target = None
    
    # Focus only on very close targets
    close_pigeons = [p for p in pigeons if p['distance'] < 500 and not p['is_fleeing']]
    
    if not close_pigeons:
        # If no close targets, look for approaching targets
        for pigeon in pigeons:
            if pigeon['is_fleeing'] or pigeon['distance'] > 800:
                continue
            
            # Check if pigeon is approaching (negative radial velocity)
            pos = pigeon['position']
            vel = pigeon['velocity']
            radial_velocity = (pos[0]*vel[0] + pos[1]*vel[1] + pos[2]*vel[2]) / max(pigeon['distance'], 1)
            
            if radial_velocity < -20:  # Approaching at least 20 units/s
                close_pigeons.append(pigeon)
    
    for pigeon in close_pigeons:
        # Ultra-aggressive scoring for close targets
        distance_score = 100.0 / (1.0 + pigeon['distance'])
        
        # Prefer stationary targets
        speed_penalty = pigeon['speed'] / 100.0
        
        # Minimal angle consideration
        angle_diff = calculate_angle_diff(
            camera['yaw'], camera['pitch'],
            pigeon['yaw'], pigeon['pitch']
        )
        angle_score = 1.0 / (1.0 + angle_diff / 60)
        
        # Fresh target bonus
        freshness = 1.0 / (1.0 + pigeon['shots_taken'] * 0.2)
        
        score = distance_score * (1.0 - speed_penalty) * angle_score * freshness
        
        if score > best_score:
            best_score = score
            
            # Minimal lead for very close targets
            if pigeon['distance'] < 300:
                lead_time = 0.1  # Almost no lead
            else:
                lead_time = calculate_lead_time(pigeon['distance'], pigeon['speed']) * 0.4
            
            future_pos = [
                pigeon['position'][0] + pigeon['velocity'][0] * lead_time,
                pigeon['position'][1] + pigeon['velocity'][1] * lead_time,
                pigeon['position'][2] + pigeon['velocity'][2] * lead_time
            ]
            
            dist = max(np.linalg.norm(future_pos), 0.001)
            future_yaw = math.degrees(math.atan2(future_pos[0], future_pos[2])) % 360
            future_pitch = math.degrees(math.asin(np.clip(future_pos[1] / dist, -1, 1)))
            
            best_target = {
                'target_id': pigeon['id'],
                'target_yaw': future_yaw,
                'target_pitch': future_pitch,
                'confidence': min(score / 50.0, 1.0)
            }
    
    return best_target
"""

    # Save the algorithm
    algo_dir = Path("meta_brain_results/custom_algorithms")
    algo_dir.mkdir(parents=True, exist_ok=True)

    algo_file = algo_dir / "aggressive_hunter.py"
    with open(algo_file, "w") as f:
        f.write(custom_algorithm)

    log.info(f"Saved custom algorithm to {algo_file}")

    # Test the algorithm in a headless experiment
    config = {
        "custom_algorithm": str(algo_file),
        "fire_cooldown": 0.2,
        "fire_cooldown_close": 0.1,
        "angle_threshold_close": 3.0,
        "angle_threshold_far": 8.0,
        "max_engagement_distance": 800,  # Shorter range to match algorithm
        "burst_fire_distance": 300,
        "max_shots_per_target": 2,
    }

    log.info("Running experiment with custom algorithm...")
    result = run_single_experiment(config, duration=30.0)

    log.info("Results:")
    log.info(f"  Hits: {result.hits}")
    log.info(f"  Shots: {result.shots_fired}")
    log.info(f"  Accuracy: {result.accuracy:.1f}%")
    log.info(f"  Hits/min: {result.hits_per_minute:.1f}")
    log.info(f"  KPI Score: {result.kpi_score:.3f}")
    log.info(f"  Escaped: {result.escaped_targets}")

    # Compare with standard algorithm
    standard_config = {
        "fire_cooldown": 0.3,
        "angle_threshold_close": 3.0,
        "angle_threshold_far": 8.0,
        "max_engagement_distance": 1200,
    }

    log.info("\nRunning experiment with standard predictive algorithm...")
    standard_result = run_single_experiment(standard_config, duration=30.0)

    log.info("Standard Results:")
    log.info(f"  Hits: {standard_result.hits}")
    log.info(f"  Shots: {standard_result.shots_fired}")
    log.info(f"  Accuracy: {standard_result.accuracy:.1f}%")
    log.info(f"  Hits/min: {standard_result.hits_per_minute:.1f}")
    log.info(f"  KPI Score: {standard_result.kpi_score:.3f}")
    log.info(f"  Escaped: {standard_result.escaped_targets}")

    # Compare performance
    log.info("\nComparison:")
    log.info(
        f"  Custom vs Standard Hits/min: {result.hits_per_minute:.1f} vs {standard_result.hits_per_minute:.1f}"
    )
    log.info(
        f"  Custom vs Standard KPI: {result.kpi_score:.3f} vs {standard_result.kpi_score:.3f}"
    )

    improvement = (
        (result.hits_per_minute - standard_result.hits_per_minute)
        / max(standard_result.hits_per_minute, 1)
    ) * 100
    log.info(f"  Performance change: {improvement:+.1f}%")


if __name__ == "__main__":
    test_custom_algorithm()
