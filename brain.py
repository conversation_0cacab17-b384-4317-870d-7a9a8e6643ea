"""AI Brain for Live Targeting Optimization"""

import threading
import time
import json
import base64
import io
import os
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import pygame
import numpy as np
from loguru import logger as log

try:
    import openai

    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    log.warning("OpenAI not available. Install with: pip install openai")

from config import CONFIG


@dataclass
class PerformanceSnapshot:
    """Snapshot of current performance metrics"""

    timestamp: str
    accuracy: float
    shots_fired: int
    hits: int
    avg_movement_time: float
    avg_reaction_time: float
    pigeons_in_range: int
    total_pigeons: int
    current_params: Dict[str, Any]


@dataclass
class OptimizationResult:
    """Result from AI brain optimization"""

    reasoning: str
    tool: str
    conclusion: str
    parameter_changes: Dict[str, float]
    confidence: float


@dataclass
class ExperimentResult:
    """Result from a 30-second experiment"""

    config_id: str
    start_time: str
    duration: float
    shots_fired: int
    hits: int
    accuracy: float
    hits_per_minute: float
    shots_per_target: float
    escaped_targets: int
    kpi_score: float
    config_params: Dict[str, Any]


class ConfigManager:
    """Manages saving and loading of successful configurations"""

    def __init__(self, config_file: str = "experiment_configs.json"):
        self.config_file = config_file
        self.configs: List[Dict] = []
        self.load_configs()

    def save_config(self, experiment: ExperimentResult):
        """Save a successful configuration"""
        config_data = {
            "id": experiment.config_id,
            "timestamp": experiment.start_time,
            "performance": {
                "accuracy": experiment.accuracy,
                "hits_per_minute": experiment.hits_per_minute,
                "kpi_score": experiment.kpi_score,
                "shots_fired": experiment.shots_fired,
                "hits": experiment.hits,
            },
            "params": experiment.config_params,
        }

        # Add to list and sort by KPI score
        self.configs.append(config_data)
        self.configs.sort(key=lambda x: x["performance"]["kpi_score"], reverse=True)

        # Keep only top configs
        if len(self.configs) > CONFIG.brain.MAX_EXPERIMENT_CONFIGS:
            self.configs = self.configs[: CONFIG.brain.MAX_EXPERIMENT_CONFIGS]

        self._save_to_file()
        log.info(
            f"Saved config {experiment.config_id} with KPI score {experiment.kpi_score:.3f}"
        )

    def get_best_config(self) -> Optional[Dict]:
        """Get the best performing configuration"""
        return self.configs[0] if self.configs else None

    def get_random_good_config(self) -> Optional[Dict]:
        """Get a random configuration from top 5"""
        if len(self.configs) >= 5:
            import random

            return random.choice(self.configs[:5])
        return self.get_best_config()

    def load_configs(self):
        """Load configurations from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r") as f:
                    self.configs = json.load(f)
                log.info(f"Loaded {len(self.configs)} saved configurations")
        except Exception as e:
            log.error(f"Failed to load configs: {e}")
            self.configs = []

    def _save_to_file(self):
        """Save configurations to file"""
        try:
            with open(self.config_file, "w") as f:
                json.dump(self.configs, f, indent=2)
        except Exception as e:
            log.error(f"Failed to save configs: {e}")


class AIBrain:
    """AI Brain for live targeting optimization using GPT-4"""

    def __init__(self, simulator_ref):
        self.simulator = simulator_ref
        self.running = False
        self.thread = None
        self.last_update = 0.0
        self.last_screenshot = 0.0

        # Experiment management
        self.experiment_active = False
        self.experiment_start_time = 0.0
        self.experiment_baseline = None
        self.config_manager = ConfigManager()

        # Performance tracking
        self.performance_history: List[PerformanceSnapshot] = []
        self.optimization_history: List[OptimizationResult] = []
        self.experiment_history: List[ExperimentResult] = []
        self.screenshots: List[bytes] = []

        # File logging
        self.log_file = None
        if CONFIG.brain.LOG_TO_FILE:
            self._init_file_logging()

        # OpenAI client
        self.client = None
        if OPENAI_AVAILABLE:
            try:
                self.client = openai.OpenAI()
                log.info("AI Brain initialized with OpenAI")
            except Exception as e:
                log.error(f"Failed to initialize OpenAI client: {e}")

    def _init_file_logging(self):
        """Initialize file logging for AI decisions"""
        try:
            self.log_file = open(CONFIG.brain.LOG_FILE_PATH, "a")
            log.info(f"AI Brain logging to {CONFIG.brain.LOG_FILE_PATH}")
        except Exception as e:
            log.error(f"Failed to initialize file logging: {e}")

    def _log_to_file(self, data: Dict):
        """Log data to file in JSONL format"""
        if self.log_file:
            try:
                json.dump(data, self.log_file)
                self.log_file.write("\n")
                self.log_file.flush()
            except Exception as e:
                log.error(f"Failed to write to log file: {e}")

    def start(self):
        """Start the AI brain in a separate thread"""
        if not CONFIG.brain.BRAIN_ENABLED:
            log.info("AI Brain disabled in config")
            return

        if not self.client:
            log.warning("AI Brain cannot start - OpenAI not available")
            return

        self.running = True
        self.thread = threading.Thread(target=self._brain_loop, daemon=True)
        self.thread.start()
        log.info("AI Brain started with experiment mode")

    def stop(self):
        """Stop the AI brain"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=2.0)

        if self.log_file:
            self.log_file.close()

        log.info("AI Brain stopped")

    def _brain_loop(self):
        """Main brain loop running in separate thread"""
        while self.running:
            try:
                current_time = time.time()

                # Take screenshots periodically
                if (
                    current_time - self.last_screenshot
                    >= CONFIG.brain.BRAIN_SCREENSHOT_INTERVAL
                ):
                    self._capture_screenshot()
                    self.last_screenshot = current_time

                # Handle experiments
                if CONFIG.brain.AUTO_EXPERIMENT_MODE:
                    self._handle_experiments(current_time)

                # Run optimization periodically (but not during experiments)
                if (
                    not self.experiment_active
                    and current_time - self.last_update
                    >= CONFIG.brain.BRAIN_UPDATE_INTERVAL
                ):
                    self._run_optimization()
                    self.last_update = current_time

                time.sleep(0.5)  # Check more frequently for experiments

            except Exception as e:
                log.error(f"Brain loop error: {e}")
                time.sleep(5.0)  # Wait before retrying

    def _handle_experiments(self, current_time: float):
        """Handle automatic experiments"""
        if not self.experiment_active:
            # Start new experiment
            self._start_experiment(current_time)
        else:
            # Check if experiment is complete
            experiment_duration = current_time - self.experiment_start_time
            if experiment_duration >= CONFIG.brain.EXPERIMENT_DURATION:
                self._end_experiment(current_time)

    def _start_experiment(self, current_time: float):
        """Start a new 30-second experiment"""
        self.experiment_active = True
        self.experiment_start_time = current_time

        # Record baseline performance
        self.experiment_baseline = self._get_performance_snapshot()

        # Optionally load a good configuration to test variations
        if len(self.experiment_history) > 0 and len(self.config_manager.configs) > 0:
            # 70% chance to use a variation of a good config, 30% chance for new exploration
            import random

            if random.random() < 0.7:
                base_config = self.config_manager.get_random_good_config()
                if base_config:
                    self._apply_config_with_variation(base_config)

        experiment_id = f"exp_{int(current_time)}"
        log.info(
            f"Started experiment {experiment_id} (duration: {CONFIG.brain.EXPERIMENT_DURATION}s)"
        )

        # Log experiment start
        self._log_to_file(
            {
                "type": "experiment_start",
                "experiment_id": experiment_id,
                "timestamp": datetime.now().isoformat(),
                "baseline_performance": asdict(self.experiment_baseline)
                if self.experiment_baseline
                else None,
                "current_config": self._get_current_config(),
            }
        )

    def _end_experiment(self, current_time: float):
        """End current experiment and analyze results"""
        if not self.experiment_active:
            return

        self.experiment_active = False
        experiment_duration = current_time - self.experiment_start_time

        # Get final performance
        final_snapshot = self._get_performance_snapshot()
        if not final_snapshot or not self.experiment_baseline:
            log.warning("Failed to get experiment results")
            return

        # Calculate experiment metrics
        shots_delta = final_snapshot.shots_fired - self.experiment_baseline.shots_fired
        hits_delta = final_snapshot.hits - self.experiment_baseline.hits
        accuracy = (hits_delta / max(shots_delta, 1)) * 100
        hits_per_minute = (hits_delta / experiment_duration) * 60
        shots_per_target = shots_delta / max(
            final_snapshot.total_pigeons - self.experiment_baseline.total_pigeons, 1
        )

        # Calculate KPI score for this experiment
        kpi_score = self._calculate_experiment_kpi(
            shots_delta, hits_delta, experiment_duration, final_snapshot
        )

        # Create experiment result
        experiment_id = f"exp_{int(self.experiment_start_time)}"

        # Calculate escaped targets delta
        escaped_delta = final_snapshot.current_params.get("brain_metrics", {}).get(
            "escaped_targets", 0
        ) - self.experiment_baseline.current_params.get("brain_metrics", {}).get(
            "escaped_targets", 0
        )

        experiment = ExperimentResult(
            config_id=experiment_id,
            start_time=datetime.fromtimestamp(self.experiment_start_time).isoformat(),
            duration=experiment_duration,
            shots_fired=shots_delta,
            hits=hits_delta,
            accuracy=accuracy,
            hits_per_minute=hits_per_minute,
            shots_per_target=shots_per_target,
            escaped_targets=escaped_delta,
            kpi_score=kpi_score,
            config_params=self._get_current_config(),
        )

        self.experiment_history.append(experiment)

        # Save good configurations
        if (
            CONFIG.brain.SAVE_EXPERIMENT_CONFIGS and kpi_score > 0.3
        ):  # Threshold for "good" performance
            self.config_manager.save_config(experiment)

        # Log experiment results
        log.info(
            f"Experiment {experiment_id} complete: {hits_delta}/{shots_delta} hits ({accuracy:.1f}%) | "
            f"KPI: {kpi_score:.3f} | HPM: {hits_per_minute:.1f}"
        )

        self._log_to_file(
            {
                "type": "experiment_end",
                "experiment": asdict(experiment),
                "timestamp": datetime.now().isoformat(),
            }
        )

        # Wait before next experiment
        time.sleep(CONFIG.brain.EXPERIMENT_COOLDOWN)

    def _calculate_experiment_kpi(
        self, shots_delta: int, hits_delta: int, duration: float, snapshot
    ) -> float:
        """Calculate KPI score for experiment"""
        if shots_delta == 0:
            return 0.0

        accuracy = hits_delta / shots_delta
        hits_per_minute = (hits_delta / duration) * 60
        efficiency = hits_delta / max(shots_delta, 1)

        # Normalize hits per minute (assume 10 hits/min is excellent)
        normalized_hpm = min(hits_per_minute / 10.0, 1.0)

        kpi_score = (
            CONFIG.brain.KPI_WEIGHTS.accuracy * accuracy
            + CONFIG.brain.KPI_WEIGHTS.hits_per_minute * normalized_hpm
            + CONFIG.brain.KPI_WEIGHTS.efficiency * efficiency
        )

        return max(0.0, kpi_score)

    def _apply_config_with_variation(self, base_config: Dict):
        """Apply a configuration with small random variations"""
        import random

        params = base_config["params"]

        # Apply small variations (±10%)
        variations = {
            "fire_cooldown": random.uniform(0.9, 1.1),
            "fire_cooldown_close": random.uniform(0.9, 1.1),
            "angle_threshold_close": random.uniform(0.9, 1.1),
            "angle_threshold_far": random.uniform(0.9, 1.1),
            "max_engagement_distance": random.uniform(0.95, 1.05),
        }

        for param_name, multiplier in variations.items():
            if param_name in params:
                new_value = params[param_name] * multiplier
                self._apply_parameter_change(param_name, new_value)

        log.info(f"Applied config variation based on {base_config['id']}")

    def _get_current_config(self) -> Dict[str, Any]:
        """Get current targeting configuration"""
        return {
            "fire_cooldown": CONFIG.targeting.FIRE_COOLDOWN,
            "fire_cooldown_close": CONFIG.targeting.FIRE_COOLDOWN_CLOSE,
            "angle_threshold_close": CONFIG.targeting.ANGLE_THRESHOLD_CLOSE,
            "angle_threshold_far": CONFIG.targeting.ANGLE_THRESHOLD_FAR,
            "max_engagement_distance": CONFIG.targeting.MAX_ENGAGEMENT_DISTANCE,
            "burst_fire_distance": CONFIG.targeting.BURST_FIRE_DISTANCE,
            "max_shots_per_target": CONFIG.targeting.MAX_SHOTS_PER_TARGET,
        }

    def _capture_screenshot(self):
        """Capture screenshot of current simulation"""
        try:
            # Get the current screen surface
            screen = self.simulator.screen
            if screen:
                # Convert pygame surface to PIL Image
                w, h = screen.get_size()
                raw = pygame.image.tostring(screen, "RGB")

                # Convert to base64 for API
                img_array = np.frombuffer(raw, dtype=np.uint8).reshape((h, w, 3))
                img_bytes = io.BytesIO()

                # Use PIL to save as JPEG
                from PIL import Image

                img = Image.fromarray(img_array)
                img.save(img_bytes, format="JPEG", quality=85)
                img_bytes.seek(0)

                # Store screenshot
                self.screenshots.append(img_bytes.getvalue())

                # Keep only recent screenshots
                if len(self.screenshots) > CONFIG.brain.BRAIN_MAX_SCREENSHOTS:
                    self.screenshots.pop(0)

                log.debug(f"Captured screenshot ({len(self.screenshots)} total)")

        except Exception as e:
            log.error(f"Failed to capture screenshot: {e}")

    def _get_performance_snapshot(self) -> PerformanceSnapshot:
        """Get current performance metrics"""
        try:
            stats = self.simulator.evaluator.get_current_stats()
            algo_stats = stats["algorithms"].get(CONFIG.targeting.TARGETING_MODE, {})
            brain_metrics = self.simulator.entity_manager.get_brain_metrics()

            # Count pigeons in range
            pigeons_in_range = 0
            total_pigeons = len(self.simulator.entity_manager.pigeons)

            for pigeon in self.simulator.entity_manager.pigeons:
                if pigeon.active and not pigeon.is_fleeing:
                    distance = np.linalg.norm(pigeon.position)
                    if distance <= CONFIG.targeting.MAX_ENGAGEMENT_DISTANCE:
                        pigeons_in_range += 1

            # Enhanced targeting parameters including KPI weights
            current_params = {
                "fire_cooldown": CONFIG.targeting.FIRE_COOLDOWN,
                "fire_cooldown_close": CONFIG.targeting.FIRE_COOLDOWN_CLOSE,
                "angle_threshold_close": CONFIG.targeting.ANGLE_THRESHOLD_CLOSE,
                "angle_threshold_far": CONFIG.targeting.ANGLE_THRESHOLD_FAR,
                "max_engagement_distance": CONFIG.targeting.MAX_ENGAGEMENT_DISTANCE,
                "burst_fire_distance": CONFIG.targeting.BURST_FIRE_DISTANCE,
                "max_shots_per_target": CONFIG.targeting.MAX_SHOTS_PER_TARGET,
                "kpi_weights": {
                    "accuracy": CONFIG.brain.KPI_WEIGHTS.accuracy,
                    "hits_per_minute": CONFIG.brain.KPI_WEIGHTS.hits_per_minute,
                    "engagement_rate": CONFIG.brain.KPI_WEIGHTS.engagement_rate,
                    "efficiency": CONFIG.brain.KPI_WEIGHTS.efficiency,
                },
                "brain_metrics": brain_metrics,
            }

            # Calculate composite KPI score
            session_duration_minutes = stats["session"].get("duration", 1) / 60.0
            hits_per_minute = algo_stats.get("hits", 0) / max(
                session_duration_minutes, 0.1
            )
            shots_per_hit = algo_stats.get("shots_fired", 1) / max(
                algo_stats.get("hits", 1), 1
            )
            engagement_rate = algo_stats.get("shots_fired", 0) / max(total_pigeons, 1)

            kpi_score = (
                CONFIG.brain.KPI_WEIGHTS.accuracy * algo_stats.get("accuracy", 0.0)
                + CONFIG.brain.KPI_WEIGHTS.hits_per_minute
                * min(hits_per_minute / 10.0, 1.0)  # Normalize to 10 hits/min max
                + CONFIG.brain.KPI_WEIGHTS.engagement_rate * min(engagement_rate, 1.0)
                + CONFIG.brain.KPI_WEIGHTS.efficiency * (1.0 / max(shots_per_hit, 1.0))
                + CONFIG.brain.KPI_WEIGHTS.missed_opportunities
                * (brain_metrics["escaped_targets"] / max(total_pigeons, 1))
                + CONFIG.brain.KPI_WEIGHTS.wasted_shots
                * max(0, (shots_per_hit - 3) / 10.0)  # Penalty for >3 shots per hit
            )

            # Add KPI score to current_params for tracking
            current_params["composite_kpi_score"] = kpi_score

            return PerformanceSnapshot(
                timestamp=datetime.now().isoformat(),
                accuracy=algo_stats.get("accuracy", 0.0) * 100,  # Convert to percentage
                shots_fired=algo_stats.get("shots_fired", 0),
                hits=algo_stats.get("hits", 0),
                avg_movement_time=stats["session"].get("avg_movement_time", 0.0),
                avg_reaction_time=stats["session"].get("avg_reaction_time", 0.0),
                pigeons_in_range=pigeons_in_range,
                total_pigeons=total_pigeons,
                current_params=current_params,
            )

        except Exception as e:
            log.error(f"Failed to get performance snapshot: {e}")
            return None

    def _run_optimization(self):
        """Run AI optimization"""
        try:
            # Get current performance
            snapshot = self._get_performance_snapshot()
            if not snapshot:
                return

            self.performance_history.append(snapshot)

            # Keep history manageable
            if len(self.performance_history) > CONFIG.brain.BRAIN_MEMORY_SIZE:
                self.performance_history.pop(0)

            # Need minimum samples before optimizing
            if len(self.performance_history) < CONFIG.brain.BRAIN_MIN_SAMPLES:
                log.debug(
                    f"Brain waiting for more samples ({len(self.performance_history)}/{CONFIG.brain.BRAIN_MIN_SAMPLES})"
                )
                return

            # Prepare data for AI
            observations = self._prepare_observations()
            screenshots_b64 = self._prepare_screenshots()

            # Call OpenAI
            result = self._call_openai(observations, screenshots_b64)
            if result:
                self._apply_optimization(result)

        except Exception as e:
            log.error(f"Optimization failed: {e}")

    def _prepare_observations(self) -> str:
        """Prepare performance observations for AI"""
        recent_performance = self.performance_history[-3:]  # Last 3 samples

        # Calculate performance trends
        if len(recent_performance) >= 2:
            accuracy_trend = (
                recent_performance[-1].accuracy - recent_performance[0].accuracy
            )
            hits_trend = recent_performance[-1].hits - recent_performance[0].hits
            shots_trend = (
                recent_performance[-1].shots_fired - recent_performance[0].shots_fired
            )
        else:
            accuracy_trend = hits_trend = shots_trend = 0

        # Get brain metrics for additional context
        brain_metrics = self.simulator.entity_manager.get_brain_metrics()

        # Include experiment history for learning
        recent_experiments = [asdict(exp) for exp in self.experiment_history[-5:]]
        best_configs = [
            config for config in self.config_manager.configs[:3]
        ]  # Top 3 configs

        observations = {
            "current_performance": asdict(self.performance_history[-1]),
            "performance_trend": [asdict(p) for p in recent_performance],
            "trends": {
                "accuracy_change": accuracy_trend,
                "hits_change": hits_trend,
                "shots_change": shots_trend,
            },
            "brain_metrics": brain_metrics,
            "optimization_history": [
                asdict(opt) for opt in self.optimization_history[-5:]
            ],  # Last 5 optimizations
            "experiment_history": recent_experiments,
            "best_configs": best_configs,
            "kpi_weights": {
                "accuracy": CONFIG.brain.KPI_WEIGHTS.accuracy,
                "hits_per_minute": CONFIG.brain.KPI_WEIGHTS.hits_per_minute,
                "engagement_rate": CONFIG.brain.KPI_WEIGHTS.engagement_rate,
                "efficiency": CONFIG.brain.KPI_WEIGHTS.efficiency,
                "missed_opportunities": CONFIG.brain.KPI_WEIGHTS.missed_opportunities,
                "wasted_shots": CONFIG.brain.KPI_WEIGHTS.wasted_shots,
            },
            "parameter_bounds": {
                "fire_cooldown": [
                    CONFIG.brain.MIN_FIRE_COOLDOWN,
                    CONFIG.brain.MAX_FIRE_COOLDOWN,
                ],
                "angle_threshold": [
                    CONFIG.brain.MIN_ANGLE_THRESHOLD,
                    CONFIG.brain.MAX_ANGLE_THRESHOLD,
                ],
                "max_engagement_distance": [
                    CONFIG.brain.MIN_ENGAGEMENT_DISTANCE,
                    CONFIG.brain.MAX_ENGAGEMENT_DISTANCE,
                ],
            },
        }

        return json.dumps(observations, indent=2)

    def _prepare_screenshots(self) -> List[str]:
        """Convert screenshots to base64 for API"""
        screenshots_b64 = []
        for screenshot in self.screenshots:
            b64_string = base64.b64encode(screenshot).decode("utf-8")
            screenshots_b64.append(b64_string)
        return screenshots_b64

    def _call_openai(
        self, observations: str, screenshots: List[str]
    ) -> Optional[OptimizationResult]:
        """Call OpenAI API for optimization suggestions"""
        try:
            # Prepare messages
            messages = [
                {"role": "system", "content": self._get_system_prompt()},
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"Current targeting performance data:\n\n{observations}\n\nPlease analyze the performance and suggest optimizations.",
                        }
                    ],
                },
            ]

            # Add screenshots
            for i, screenshot_b64 in enumerate(screenshots):
                messages[-1]["content"].append(
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{screenshot_b64}",
                            "detail": "low",
                        },
                    }
                )

            # Call API
            response = self.client.chat.completions.create(
                model=CONFIG.brain.OPENAI_MODEL,
                messages=messages,
                max_tokens=CONFIG.brain.OPENAI_MAX_TOKENS,
                temperature=CONFIG.brain.OPENAI_TEMPERATURE,
                response_format={
                    "type": "json_schema",
                    "json_schema": {
                        "name": "optimization_result",
                        "schema": {
                            "type": "object",
                            "properties": {
                                "reasoning": {"type": "string"},
                                "tool": {"type": "string"},
                                "conclusion": {"type": "string"},
                                "parameter_changes": {
                                    "type": "object",
                                    "additionalProperties": {"type": "number"},
                                },
                                "confidence": {
                                    "type": "number",
                                    "minimum": 0,
                                    "maximum": 1,
                                },
                            },
                            "required": [
                                "reasoning",
                                "tool",
                                "conclusion",
                                "parameter_changes",
                                "confidence",
                            ],
                        },
                    },
                },
            )

            # Parse response
            result_data = json.loads(response.choices[0].message.content)
            result = OptimizationResult(**result_data)

            log.info(
                f"AI Brain optimization: {result.conclusion} (confidence: {result.confidence:.2f})"
            )
            log.debug(f"AI reasoning: {result.reasoning}")

            # Log to file
            self._log_to_file(
                {
                    "type": "optimization",
                    "result": asdict(result),
                    "timestamp": datetime.now().isoformat(),
                    "observations_summary": {
                        "current_accuracy": self.performance_history[-1].accuracy
                        if self.performance_history
                        else 0,
                        "shots_fired": self.performance_history[-1].shots_fired
                        if self.performance_history
                        else 0,
                        "hits": self.performance_history[-1].hits
                        if self.performance_history
                        else 0,
                    },
                }
            )

            return result

        except Exception as e:
            log.error(f"OpenAI API call failed: {e}")
            return None

    def _get_system_prompt(self) -> str:
        """Get the system prompt for the AI"""
        return """You are an AI targeting optimization expert for an automated anti-pigeon turret system. 

Your PRIMARY GOAL is to maximize the composite KPI score: f(pigeons_hit + accuracy) with weighted factors.

CURRENT SYSTEM:
- Predictive targeting algorithm with 3D movement compensation
- Dynamic pigeon management (maintains 6-8 targets in range)
- Adaptive fire rates and burst fire for close targets
- Shot limiting (max 3 shots per pigeon)
- Automatic search behavior when no targets available
- Enhanced pigeon spawning with relevance checking
- 30-second experiment cycles with configuration saving

KEY PERFORMANCE INDICATORS (with weights):
- accuracy (40%): Hit/shot ratio - primary efficiency metric
- hits_per_minute (30%): Absolute hit rate - engagement effectiveness  
- engagement_rate (20%): Shots fired per target available - opportunity utilization
- efficiency (10%): Inverse of shots per hit - resource conservation
- missed_opportunities (-10%): Penalty for targets that escape unengaged
- wasted_shots (-5%): Penalty for excessive shots at same target

ENHANCED METRICS AVAILABLE:
- escaped_targets: Targets that left without being engaged
- recent_escapes: Escapes in last 60 seconds
- engagement_opportunities: Missed engagement windows
- active_pigeons: Currently active targets
- fleeing_pigeons: Targets hit and fleeing
- pigeons_with_shots: Targets currently being engaged
- relevant_spawns: Pigeons with trajectories crossing turret range

EXPERIMENT DATA AVAILABLE:
- experiment_history: Results from recent 30-second experiments
- best_configs: Top-performing configurations from experiments
- You can see which parameter combinations worked best

KEY PARAMETERS YOU CAN OPTIMIZE:
- fire_cooldown: Time between shots (0.1-1.0s)
- fire_cooldown_close: Faster rate for close targets (0.1-1.0s)
- angle_threshold_close: Precision for close targets (1-15°)
- angle_threshold_far: Precision for far targets (1-15°)
- max_engagement_distance: Maximum targeting range (500-2000 units)
- burst_fire_distance: Distance threshold for burst fire (100-500 units)
- max_shots_per_target: Shot limit per pigeon (1-5 shots)

OPTIMIZATION STRATEGY:
1. Analyze experiment history to identify successful patterns
2. Look at best_configs to understand what parameters work well together
3. Consider the current KPI score vs. historical best performance
4. Focus on parameters that have shown improvement in experiments
5. Make conservative adjustments (10-20% changes) based on proven patterns
6. Use visual feedback to understand target density and movement patterns
7. Justify decisions based on actual controllable parameters and experiment data

RESPONSE FORMAT:
- reasoning: Detailed analysis focusing on experiment data and proven patterns
- tool: The optimization approach (e.g., "experiment_based_tuning", "best_config_variation")
- conclusion: Summary of expected KPI improvements based on experiment history
- parameter_changes: Dict of parameter names to new values (based on successful experiments)
- confidence: Your confidence in this optimization improving the composite KPI (0.0-1.0)

Focus on changes that have been proven effective in the experiment history. Reference specific experiment results when justifying parameter changes."""

    def _apply_optimization(self, result: OptimizationResult):
        """Apply optimization results to the targeting system"""
        try:
            if result.confidence < 0.3:
                log.warning(
                    f"Low confidence optimization ignored: {result.confidence:.2f}"
                )
                return

            changes_applied = 0

            for param_name, new_value in result.parameter_changes.items():
                if self._apply_parameter_change(param_name, new_value):
                    changes_applied += 1

            if changes_applied > 0:
                self.optimization_history.append(result)
                log.info(f"Applied {changes_applied} parameter changes from AI Brain")
            else:
                log.warning("No valid parameter changes could be applied")

        except Exception as e:
            log.error(f"Failed to apply optimization: {e}")

    def _apply_parameter_change(self, param_name: str, new_value: float) -> bool:
        """Apply a single parameter change with bounds checking"""
        try:
            # Bounds checking
            if param_name in ["fire_cooldown", "fire_cooldown_close"]:
                new_value = max(
                    CONFIG.brain.MIN_FIRE_COOLDOWN,
                    min(CONFIG.brain.MAX_FIRE_COOLDOWN, new_value),
                )
            elif param_name in ["angle_threshold_close", "angle_threshold_far"]:
                new_value = max(
                    CONFIG.brain.MIN_ANGLE_THRESHOLD,
                    min(CONFIG.brain.MAX_ANGLE_THRESHOLD, new_value),
                )
            elif param_name == "max_engagement_distance":
                new_value = max(
                    CONFIG.brain.MIN_ENGAGEMENT_DISTANCE,
                    min(CONFIG.brain.MAX_ENGAGEMENT_DISTANCE, new_value),
                )
            elif param_name == "max_shots_per_target":
                new_value = max(1, min(5, int(new_value)))
            elif param_name == "burst_fire_distance":
                new_value = max(100, min(500, new_value))
            else:
                log.warning(f"Unknown parameter: {param_name}")
                return False

            # Apply the change
            if hasattr(CONFIG.targeting, param_name.upper()):
                old_value = getattr(CONFIG.targeting, param_name.upper())
                setattr(CONFIG.targeting, param_name.upper(), new_value)
                log.info(f"Updated {param_name}: {old_value:.3f} → {new_value:.3f}")
                return True
            else:
                log.warning(f"Parameter not found in config: {param_name}")
                return False

        except Exception as e:
            log.error(f"Failed to apply parameter {param_name}={new_value}: {e}")
            return False
