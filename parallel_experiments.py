"""Parallel Experiment Manager for Turret Optimization"""

import multiprocessing as mp
import time
import json
import random
import numpy as np
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from concurrent.futures import ProcessPoolExecutor, as_completed
from loguru import logger as log

from headless_simulator import run_single_experiment, ExperimentResult


@dataclass
class ExperimentBatch:
    """A batch of experiments to run in parallel"""

    batch_id: str
    configs: List[Dict[str, Any]]
    duration: float = 30.0


@dataclass
class BatchResult:
    """Results from a batch of parallel experiments"""

    batch_id: str
    start_time: str
    duration: float
    experiments: List[ExperimentResult]
    best_config: Dict[str, Any]
    best_kpi: float
    avg_kpi: float


class ParameterSpace:
    """Defines the parameter space for optimization"""

    def __init__(self):
        self.parameters = {
            "fire_cooldown": (0.1, 1.0),
            "fire_cooldown_close": (0.05, 0.5),
            "angle_threshold_close": (1.0, 8.0),
            "angle_threshold_far": (3.0, 15.0),
            "max_engagement_distance": (600.0, 1800.0),
            "burst_fire_distance": (100.0, 400.0),
        }

    def random_config(self) -> Dict[str, Any]:
        """Generate a random configuration within parameter bounds"""
        config = {}
        for param, (min_val, max_val) in self.parameters.items():
            config[param] = random.uniform(min_val, max_val)
        return config

    def grid_configs(self, steps_per_param: int = 3) -> List[Dict[str, Any]]:
        """Generate grid search configurations"""
        configs = []
        param_names = list(self.parameters.keys())

        # Generate grid points for each parameter
        param_values = {}
        for param, (min_val, max_val) in self.parameters.items():
            param_values[param] = np.linspace(min_val, max_val, steps_per_param)

        # Generate all combinations (this can get large quickly)
        import itertools

        for combination in itertools.product(*param_values.values()):
            config = dict(zip(param_names, combination))
            configs.append(config)

        return configs

    def mutate_config(
        self, base_config: Dict[str, Any], mutation_rate: float = 0.1
    ) -> Dict[str, Any]:
        """Create a mutated version of a configuration"""
        config = base_config.copy()

        for param, value in config.items():
            if param in self.parameters and random.random() < mutation_rate:
                min_val, max_val = self.parameters[param]
                # Add gaussian noise (10% of range)
                noise = random.gauss(0, (max_val - min_val) * 0.1)
                new_value = value + noise
                # Clamp to bounds
                config[param] = max(min_val, min(max_val, new_value))

        return config


class ParallelExperimentManager:
    """Manages parallel experiments for turret optimization"""

    def __init__(self, max_workers: Optional[int] = None):
        self.max_workers = max_workers or min(
            mp.cpu_count(), 8
        )  # Cap at 8 to avoid overwhelming
        self.parameter_space = ParameterSpace()
        self.experiment_history: List[ExperimentResult] = []
        self.batch_history: List[BatchResult] = []

        log.info(
            f"Parallel Experiment Manager initialized with {self.max_workers} workers"
        )

    def run_random_search(
        self, num_experiments: int, duration: float = 30.0
    ) -> BatchResult:
        """Run random search experiments in parallel"""
        configs = [self.parameter_space.random_config() for _ in range(num_experiments)]

        batch = ExperimentBatch(
            batch_id=f"random_search_{int(time.time())}",
            configs=configs,
            duration=duration,
        )

        return self._run_batch(batch)

    def run_grid_search(
        self, steps_per_param: int = 3, duration: float = 30.0
    ) -> BatchResult:
        """Run grid search experiments in parallel"""
        configs = self.parameter_space.grid_configs(steps_per_param)

        # Limit grid size to avoid overwhelming
        if len(configs) > 50:
            log.warning(
                f"Grid search would generate {len(configs)} experiments. Sampling 50 random ones."
            )
            configs = random.sample(configs, 50)

        batch = ExperimentBatch(
            batch_id=f"grid_search_{int(time.time())}",
            configs=configs,
            duration=duration,
        )

        return self._run_batch(batch)

    def run_genetic_optimization(
        self, population_size: int = 20, generations: int = 5, duration: float = 30.0
    ) -> List[BatchResult]:
        """Run genetic algorithm optimization"""
        results = []

        # Initial population
        population = [
            self.parameter_space.random_config() for _ in range(population_size)
        ]

        for generation in range(generations):
            log.info(
                f"Running genetic optimization generation {generation + 1}/{generations}"
            )

            batch = ExperimentBatch(
                batch_id=f"genetic_gen_{generation}_{int(time.time())}",
                configs=population,
                duration=duration,
            )

            batch_result = self._run_batch(batch)
            results.append(batch_result)

            # Select best performers for next generation
            experiments = sorted(
                batch_result.experiments, key=lambda x: x.kpi_score, reverse=True
            )

            # Keep top 50% as parents
            parents = experiments[: population_size // 2]

            # Generate next population
            new_population = []

            # Keep best performers
            for parent in parents[: population_size // 4]:
                new_population.append(parent.config_params)

            # Create mutations of best performers
            for parent in parents:
                mutated = self.parameter_space.mutate_config(
                    parent.config_params, mutation_rate=0.2
                )
                new_population.append(mutated)

            # Fill remaining with random configs
            while len(new_population) < population_size:
                new_population.append(self.parameter_space.random_config())

            population = new_population[:population_size]

            log.info(
                f"Generation {generation + 1} complete. Best KPI: {batch_result.best_kpi:.3f}"
            )

        return results

    def run_exploitation_search(
        self,
        base_configs: List[Dict[str, Any]],
        variations_per_config: int = 10,
        duration: float = 30.0,
    ) -> BatchResult:
        """Run exploitation search around known good configurations"""
        configs = []

        for base_config in base_configs:
            # Add the base config
            configs.append(base_config)

            # Add variations
            for _ in range(variations_per_config):
                mutated = self.parameter_space.mutate_config(
                    base_config, mutation_rate=0.15
                )
                configs.append(mutated)

        batch = ExperimentBatch(
            batch_id=f"exploitation_{int(time.time())}",
            configs=configs,
            duration=duration,
        )

        return self._run_batch(batch)

    def _run_batch(self, batch: ExperimentBatch) -> BatchResult:
        """Run a batch of experiments in parallel"""
        start_time = time.time()

        log.info(
            f"Starting batch {batch.batch_id} with {len(batch.configs)} experiments"
        )

        # Run experiments in parallel
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all experiments
            future_to_config = {
                executor.submit(run_single_experiment, config, batch.duration): config
                for config in batch.configs
            }

            results = []
            completed = 0

            # Collect results as they complete
            for future in as_completed(future_to_config):
                try:
                    result = future.result()
                    results.append(result)
                    completed += 1

                    if completed % 5 == 0 or completed == len(batch.configs):
                        log.info(
                            f"Completed {completed}/{len(batch.configs)} experiments"
                        )

                except Exception as e:
                    log.error(f"Experiment failed: {e}")

        # Analyze results
        if results:
            best_result = max(results, key=lambda x: x.kpi_score)
            avg_kpi = sum(r.kpi_score for r in results) / len(results)
        else:
            best_result = None
            avg_kpi = 0.0

        batch_result = BatchResult(
            batch_id=batch.batch_id,
            start_time=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time)),
            duration=time.time() - start_time,
            experiments=results,
            best_config=best_result.config_params if best_result else {},
            best_kpi=best_result.kpi_score if best_result else 0.0,
            avg_kpi=avg_kpi,
        )

        # Store results
        self.experiment_history.extend(results)
        self.batch_history.append(batch_result)

        log.info(
            f"Batch {batch.batch_id} complete in {batch_result.duration:.1f}s. "
            f"Best KPI: {batch_result.best_kpi:.3f}, Avg KPI: {batch_result.avg_kpi:.3f}"
        )

        return batch_result

    def get_best_configs(self, top_n: int = 10) -> List[ExperimentResult]:
        """Get the top N best configurations found so far"""
        return sorted(self.experiment_history, key=lambda x: x.kpi_score, reverse=True)[
            :top_n
        ]

    def save_results(self, filename: str = "parallel_experiment_results.json"):
        """Save all experiment results to file"""
        data = {
            "batches": [asdict(batch) for batch in self.batch_history],
            "all_experiments": [asdict(exp) for exp in self.experiment_history],
            "summary": {
                "total_experiments": len(self.experiment_history),
                "total_batches": len(self.batch_history),
                "best_kpi": max(exp.kpi_score for exp in self.experiment_history)
                if self.experiment_history
                else 0.0,
                "avg_kpi": sum(exp.kpi_score for exp in self.experiment_history)
                / len(self.experiment_history)
                if self.experiment_history
                else 0.0,
            },
        }

        with open(filename, "w") as f:
            json.dump(data, f, indent=2)

        log.info(f"Results saved to {filename}")


def main():
    """Example usage of parallel experiment manager"""
    manager = ParallelExperimentManager(max_workers=4)

    # Run a small random search
    log.info("Running random search with 8 experiments...")
    random_results = manager.run_random_search(num_experiments=8, duration=15.0)

    print("\nRandom Search Results:")
    print(f"Best KPI: {random_results.best_kpi:.3f}")
    print(f"Average KPI: {random_results.avg_kpi:.3f}")
    print(f"Best Config: {random_results.best_config}")

    # Get best configs for exploitation
    best_configs = [exp.config_params for exp in manager.get_best_configs(top_n=3)]

    if best_configs:
        log.info("Running exploitation search around best configs...")
        exploitation_results = manager.run_exploitation_search(
            base_configs=best_configs, variations_per_config=3, duration=15.0
        )

        print("\nExploitation Search Results:")
        print(f"Best KPI: {exploitation_results.best_kpi:.3f}")
        print(f"Average KPI: {exploitation_results.avg_kpi:.3f}")

    # Save results
    manager.save_results()

    # Print summary
    all_best = manager.get_best_configs(top_n=5)
    print("\nTop 5 Configurations:")
    for i, exp in enumerate(all_best, 1):
        print(
            f"{i}. KPI: {exp.kpi_score:.3f}, Accuracy: {exp.accuracy:.1f}%, "
            f"HPM: {exp.hits_per_minute:.1f}"
        )


if __name__ == "__main__":
    main()
