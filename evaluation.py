"""Performance evaluation and metrics tracking for turret system"""
import time
import numpy as np
from dataclasses import dataclass, field
from typing import List, Dict, Optional
from loguru import logger as log
import json

@dataclass
class ShotMetrics:
    """Metrics for a single shot"""
    shot_time: float
    target_id: int
    algorithm: str
    movement_time: float  # Time to aim
    projectile_flight_time: float
    hit: bool
    distance: float
    target_speed: float
    angular_velocity: float

@dataclass
class AlgorithmMetrics:
    """Performance metrics for a targeting algorithm"""
    name: str
    shots_fired: int = 0
    hits: int = 0
    total_movement_time: float = 0.0
    total_reaction_time: float = 0.0
    shot_history: List[ShotMetrics] = field(default_factory=list)
    
    @property
    def accuracy(self) -> float:
        """Hit rate percentage"""
        return (self.hits / self.shots_fired * 100) if self.shots_fired > 0 else 0.0
        
    @property
    def avg_movement_time(self) -> float:
        """Average time to aim at target"""
        return self.total_movement_time / self.shots_fired if self.shots_fired > 0 else 0.0
        
    @property
    def avg_reaction_time(self) -> float:
        """Average time from detection to fire"""
        return self.total_reaction_time / self.shots_fired if self.shots_fired > 0 else 0.0

class PerformanceEvaluator:
    """Tracks and evaluates turret performance"""
    
    def __init__(self):
        self.start_time = time.time()
        self.algorithms: Dict[str, AlgorithmMetrics] = {}
        self.session_metrics = {
            "total_pigeons_spawned": 0,
            "total_pigeons_hit": 0,
            "total_shots_fired": 0,
            "session_duration": 0.0
        }
        self.current_targets: Dict[int, float] = {}  # pigeon_id -> first_detection_time
        
    def register_algorithm(self, name: str):
        """Register a new algorithm for tracking"""
        if name not in self.algorithms:
            self.algorithms[name] = AlgorithmMetrics(name=name)
            
    def record_pigeon_spawn(self, pigeon_id: int, spawn_time: float):
        """Record when a pigeon is spawned"""
        self.session_metrics["total_pigeons_spawned"] += 1
        self.current_targets[pigeon_id] = spawn_time
        
    def record_shot(self, shot_metrics: ShotMetrics):
        """Record a shot fired"""
        algo_name = shot_metrics.algorithm
        if algo_name not in self.algorithms:
            self.register_algorithm(algo_name)
            
        algo_metrics = self.algorithms[algo_name]
        algo_metrics.shots_fired += 1
        algo_metrics.total_movement_time += shot_metrics.movement_time
        algo_metrics.shot_history.append(shot_metrics)
        
        # Calculate reaction time if we have detection time
        if shot_metrics.target_id in self.current_targets:
            detection_time = self.current_targets[shot_metrics.target_id]
            reaction_time = shot_metrics.shot_time - detection_time
            algo_metrics.total_reaction_time += reaction_time
            
        self.session_metrics["total_shots_fired"] += 1
        
        if shot_metrics.hit:
            algo_metrics.hits += 1
            self.session_metrics["total_pigeons_hit"] += 1
            # Remove from current targets
            self.current_targets.pop(shot_metrics.target_id, None)
            
        log.info(f"Shot recorded - Algorithm: {algo_name}, Hit: {shot_metrics.hit}, "
                f"Distance: {shot_metrics.distance:.0f}, Movement time: {shot_metrics.movement_time:.2f}s")
                
    def get_current_stats(self) -> Dict:
        """Get current performance statistics"""
        current_time = time.time()
        self.session_metrics["session_duration"] = current_time - self.start_time
        
        stats = {
            "session": self.session_metrics,
            "algorithms": {}
        }
        
        for name, metrics in self.algorithms.items():
            stats["algorithms"][name] = {
                "shots_fired": metrics.shots_fired,
                "hits": metrics.hits,
                "accuracy": metrics.accuracy,
                "avg_movement_time": metrics.avg_movement_time,
                "avg_reaction_time": metrics.avg_reaction_time
            }
            
        return stats
        
    def generate_report(self) -> str:
        """Generate a detailed performance report"""
        stats = self.get_current_stats()
        
        report_lines = [
            "=== Turret Performance Report ===",
            f"Session Duration: {stats['session']['session_duration']:.1f}s",
            f"Total Pigeons Spawned: {stats['session']['total_pigeons_spawned']}",
            f"Total Pigeons Hit: {stats['session']['total_pigeons_hit']}",
            f"Total Shots Fired: {stats['session']['total_shots_fired']}",
            "",
            "=== Algorithm Performance ==="
        ]
        
        for algo_name, algo_stats in stats['algorithms'].items():
            report_lines.extend([
                f"\n{algo_name} Algorithm:",
                f"  Shots Fired: {algo_stats['shots_fired']}",
                f"  Hits: {algo_stats['hits']}",
                f"  Accuracy: {algo_stats['accuracy']:.1f}%",
                f"  Avg Movement Time: {algo_stats['avg_movement_time']:.2f}s",
                f"  Avg Reaction Time: {algo_stats['avg_reaction_time']:.2f}s"
            ])
            
        # Add comparative analysis if multiple algorithms
        if len(stats['algorithms']) > 1:
            report_lines.extend([
                "",
                "=== Comparative Analysis ===",
            ])
            
            # Find best performing algorithm
            best_accuracy = 0
            best_algo = None
            for algo_name, algo_stats in stats['algorithms'].items():
                if algo_stats['accuracy'] > best_accuracy and algo_stats['shots_fired'] > 5:
                    best_accuracy = algo_stats['accuracy']
                    best_algo = algo_name
                    
            if best_algo:
                report_lines.append(f"Best Accuracy: {best_algo} ({best_accuracy:.1f}%)")
                
        return "\n".join(report_lines)
        
    def save_metrics(self, filepath: str = "turret_metrics.json"):
        """Save metrics to file"""
        stats = self.get_current_stats()
        
        # Add detailed shot history
        stats["shot_history"] = {}
        for algo_name, metrics in self.algorithms.items():
            stats["shot_history"][algo_name] = [
                {
                    "time": shot.shot_time,
                    "hit": shot.hit,
                    "distance": shot.distance,
                    "movement_time": shot.movement_time,
                    "target_speed": shot.target_speed
                }
                for shot in metrics.shot_history
            ]
            
        try:
            with open(filepath, 'w') as f:
                json.dump(stats, f, indent=2)
            log.info(f"Metrics saved to {filepath}")
        except Exception as e:
            log.error(f"Failed to save metrics: {e}")
            
    def plot_performance(self):
        """Generate performance plots (requires matplotlib)"""
        try:
            import matplotlib.pyplot as plt
            
            # Accuracy comparison
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
            
            # Algorithm accuracy bar chart
            algo_names = list(self.algorithms.keys())
            accuracies = [self.algorithms[name].accuracy for name in algo_names]
            shots_fired = [self.algorithms[name].shots_fired for name in algo_names]
            
            ax1.bar(algo_names, accuracies)
            ax1.set_ylabel('Accuracy (%)')
            ax1.set_title('Algorithm Accuracy Comparison')
            ax1.set_ylim(0, 100)
            
            # Shots fired comparison
            ax2.bar(algo_names, shots_fired)
            ax2.set_ylabel('Shots Fired')
            ax2.set_title('Shots Fired by Algorithm')
            
            plt.tight_layout()
            plt.savefig('turret_performance.png')
            log.info("Performance plot saved to turret_performance.png")
            
        except ImportError:
            log.warning("Matplotlib not available for plotting") 