# Targeting Algorithm Sequence Diagram & Bug Analysis

## Current Flow (BUGGY)

```mermaid
sequenceDiagram
    participant G<PERSON> as Game Loop
    participant TS as TargetingSystem
    participant <PERSON><PERSON> as MultiTargetPlanner
    participant C as Camera
    participant EM as EntityManager
    
    loop Every Frame (60 FPS)
        GL->>TS: select_target()
        
        alt Multi-target enabled & plan expired/missing
            Note over TS: ISSUE 1: Re-plans EVERY FRAME when no plan
            TS->>MTP: plan_engagement()
            
            Note over MTP: ISSUE 2: Should pause here but doesn't
            MTP->>MTP: Filter & score targets
            MTP->>MTP: Optimize path
            MTP->>MTP: Generate plan with predictions
            MTP-->>TS: EngagementPlan
            
            Note over TS: Sets current_plan
        end
        
        alt Has valid plan
            TS->>TS: Get current target from plan
            Note over TS: ISSUE 3: Uses predicted position directly
            TS-->>GL: TargetingDecision
        else No plan or single-target
            TS->>TS: Use single-target algorithm
            TS-->>GL: TargetingDecision
        end
        
        GL->>C: set_target_angles()
        GL->>C: update() - moves turret
        
        alt Aimed & can fire
            GL->>EM: fire_projectile()
            Note over TS: ISSUE 4: Advances plan index immediately
            TS->>TS: current_plan_index++
        end
    end
```

## Major Issues Identified

### 1. **Continuous Re-planning**
- The system checks for a new plan EVERY FRAME in `select_target()`
- Condition: `if self.current_engagement_plan is None or self.current_plan_index >= len(self.current_engagement_plan.targets)`
- This means as soon as it fires at all targets, it immediately re-plans
- No cooldown or execution completion check

### 2. **Pause Not Working**
- `CONFIG.PAUSE_WHEN_CALCULATING = True` but pause only works in simulator, not headless
- In `targeting.py` line ~430: `if simulator and CONFIG.multi_target.PAUSE_WHEN_CALCULATING:`
- But simulator is only passed in main.py, not in headless_simulator.py
- The pause mechanism is incomplete

### 3. **Incorrect Prediction Usage**
- The plan stores predicted positions at planning time
- But these predictions are used directly when executing, regardless of elapsed time
- The predicted position should be recalculated based on current time
- Currently: Uses `target_plan.predicted_position` directly (line ~480)
- Should: Recalculate based on `current_time - planning_time`

### 4. **No Plan Execution Tracking**
- The system advances `current_plan_index` immediately after firing
- No tracking of whether the shot hit or missed
- No waiting for projectile to reach target
- No concept of "plan in execution" vs "plan complete"

## Correct Flow (PROPOSED)

```mermaid
sequenceDiagram
    participant GL as Game Loop
    participant TS as TargetingSystem
    participant MTP as MultiTargetPlanner
    participant C as Camera
    participant EM as EntityManager
    
    alt No active plan
        GL->>GL: Pause simulation
        GL->>TS: request_new_plan()
        TS->>MTP: plan_engagement()
        MTP->>MTP: Calculate optimal sequence
        MTP-->>TS: EngagementPlan
        TS->>TS: Set plan state = EXECUTING
        GL->>GL: Resume simulation
    end
    
    loop While plan executing
        GL->>TS: get_current_target()
        
        alt Current target still valid
            TS->>TS: Recalculate prediction
            Note over TS: pred_pos = initial_pos + velocity * (now - plan_time)
            TS-->>GL: Updated TargetingDecision
            
            GL->>C: set_target_angles()
            GL->>C: update()
            
            alt Aimed & can fire
                GL->>EM: fire_projectile()
                TS->>TS: Mark target as "shot pending"
                Note over TS: Wait for hit/miss confirmation
            end
            
            alt Shot result received
                TS->>TS: Update plan progress
                alt All targets engaged
                    TS->>TS: Set plan state = COMPLETE
                end
            end
        else Target invalid
            TS->>TS: Skip to next target
        end
    end
```

## Key Fixes Needed

1. **Add Plan State Machine**
   - States: IDLE, PLANNING, EXECUTING, COMPLETE
   - Only re-plan when COMPLETE or timeout

2. **Fix Pause Mechanism**
   - Pass simulator reference to headless targeting
   - Or implement pause at game loop level

3. **Fix Prediction Calculation**
   - Store initial position and velocity in plan
   - Recalculate prediction based on elapsed time
   - Account for turret movement time

4. **Add Execution Tracking**
   - Track shots in flight
   - Wait for hit/miss before advancing
   - Add timeout for missed shots

5. **Add Planning Cooldown**
   - Minimum time between plans
   - Prevent continuous re-planning 