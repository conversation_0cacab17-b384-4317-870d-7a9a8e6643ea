"""Focused regression tests for turret targeting system"""

import time
import math
import numpy as np
from loguru import logger as log
from dataclasses import dataclass
from typing import List, Optional

from headless_simulator import HeadlessTurretSimulator, HeadlessConfig
from entities import Pigeon
from config import CONFIG

# Configure minimal logging for tests
log.remove()
log.add(lambda msg: print(msg), level="INFO", format="{message}")


@dataclass
class TestResult:
    """Result of a focused test"""

    test_name: str
    duration: float
    shots_fired: int
    hits: int
    hit_rate: float
    pigeons_spawned: int
    success: bool
    details: str
    multi_target_attempts: int = 0


class FocusedTester:
    """Fast, focused regression tests"""

    def __init__(self):
        self.results: List[TestResult] = []

    def run_all_tests(self) -> List[TestResult]:
        """Run all focused tests"""
        log.info("🎯 Running Focused Regression Tests")
        log.info("=" * 50)

        # Test 1: Multi-target close cluster
        self.test_multi_target_close_cluster()

        # Test 2: Single target stationary
        self.test_single_target_stationary()

        # Test 3: Single target moving predictably
        self.test_single_target_moving()

        # Test 4: Multi-target with max 2 targets
        self.test_multi_target_limited()

        # Test 5: Opportunistic algorithm test
        self.test_opportunistic_close_targets()

        self._print_summary()
        return self.results

    def test_multi_target_close_cluster(self):
        """Test: 5 pigeons close together, max 2 targets, ONE multi-target attempt"""
        log.info("\n🎯 Test 1: Multi-target close cluster (ONE attempt)")

        # Configure for multi-target with max 2 targets
        original_multi_enabled = CONFIG.multi_target.MULTI_TARGET_ENABLED
        original_multi_max = CONFIG.multi_target.MULTI_TARGET_MAX
        original_min_targets = CONFIG.multi_target.MIN_TARGETS_FOR_MULTI

        CONFIG.multi_target.MULTI_TARGET_ENABLED = True
        CONFIG.multi_target.MULTI_TARGET_MAX = 2  # Max 2 targets as requested
        CONFIG.multi_target.MIN_TARGETS_FOR_MULTI = (
            2  # Need at least 2 for multi-target
        )

        try:
            config = {
                "fire_cooldown": 0.1,  # Fast firing
                "angle_threshold_close": 3.0,  # Precise aiming
                "max_engagement_distance": 800,
            }

            # Create simulator - short duration for ONE attempt
            headless_config = HeadlessConfig(duration=4.0, log_level="INFO")
            simulator = HeadlessTurretSimulator(config, headless_config)

            # Disable automatic pigeon spawning by setting very low rate
            original_spawn_rate = CONFIG.environment.PIGEON_SPAWN_RATE
            CONFIG.environment.PIGEON_SPAWN_RATE = 0.001  # Very low spawn rate

            # Spawn exactly 5 pigeons in tight cluster
            current_time = time.time()
            cluster_center = np.array(
                [0.0, 80.0, 450.0], dtype=float
            )  # Close, straight ahead

            for i in range(5):
                # Very tight cluster - small offsets
                angle = i * (2 * math.pi / 5)  # Evenly spaced in circle
                radius = 30  # Small radius
                offset = np.array(
                    [
                        radius * math.cos(angle),
                        np.random.uniform(-10, 10),  # Small vertical variation
                        radius * math.sin(angle),
                    ],
                    dtype=float,
                )
                position = cluster_center + offset

                # Very slow movement to keep cluster stable
                velocity = np.array(
                    [
                        np.random.uniform(-5, 5),
                        np.random.uniform(-2, 2),
                        np.random.uniform(-5, 5),
                    ],
                    dtype=float,
                )

                pigeon = Pigeon(
                    position=position,
                    velocity=velocity,
                    size=30,
                    active=True,
                    id=i,
                    spawn_time=current_time,
                )
                simulator.entity_manager.pigeons.append(pigeon)

            simulator.entity_manager.next_pigeon_id = 5

            # Run for short duration - just enough for ONE multi-target attempt
            start_time = time.time()
            result = simulator.run_experiment()
            actual_duration = time.time() - start_time

            # Count multi-target attempts from logs (would need to capture this properly)
            # For now, evaluate based on whether it hit both targets efficiently
            targets_hit = result.hits
            shots_fired = result.shots_fired

            # Success criteria: Hit at least 2 targets with reasonable efficiency
            # (Should be able to hit 2 out of 5 close targets in one attempt)
            success = targets_hit >= 2 and shots_fired <= 6  # Max 3 shots per target

            details = f"ONE attempt: {targets_hit}/2 targets hit, {shots_fired} shots, 5 pigeons"

            test_result = TestResult(
                test_name="multi_target_close_cluster_one_attempt",
                duration=actual_duration,
                shots_fired=shots_fired,
                hits=targets_hit,
                hit_rate=result.accuracy,
                pigeons_spawned=5,
                success=success,
                details=details,
            )

            self.results.append(test_result)
            log.info(f"   Result: {'✅ PASS' if success else '❌ FAIL'} - {details}")

        finally:
            # Restore original config
            CONFIG.multi_target.MULTI_TARGET_ENABLED = original_multi_enabled
            CONFIG.multi_target.MULTI_TARGET_MAX = original_multi_max
            CONFIG.multi_target.MIN_TARGETS_FOR_MULTI = original_min_targets
            CONFIG.environment.PIGEON_SPAWN_RATE = original_spawn_rate

    def test_single_target_stationary(self):
        """Test: Single stationary pigeon, should hit reliably"""
        log.info("\n🎯 Test 2: Single stationary target")

        # Disable multi-target for pure single-target test
        original_multi_enabled = CONFIG.multi_target.MULTI_TARGET_ENABLED
        CONFIG.multi_target.MULTI_TARGET_ENABLED = False

        try:
            config = {
                "fire_cooldown": 0.2,
                "angle_threshold_close": 3.0,
                "max_engagement_distance": 1000,
            }

            headless_config = HeadlessConfig(duration=6.0, log_level="INFO")
            simulator = HeadlessTurretSimulator(config, headless_config)

            # Single stationary pigeon straight ahead
            pigeon = Pigeon(
                position=np.array(
                    [0.0, 50.0, 400.0], dtype=float
                ),  # Straight ahead, close
                velocity=np.array([0.0, 0.0, 0.0], dtype=float),  # Stationary
                size=30,
                active=True,
                id=0,
                spawn_time=time.time(),
            )
            simulator.entity_manager.pigeons.append(pigeon)
            simulator.entity_manager.next_pigeon_id = 1

            start_time = time.time()
            result = simulator.run_experiment()
            actual_duration = time.time() - start_time

            # Should hit stationary target easily
            success = result.hits >= 1 and result.accuracy > 50
            details = f"Stationary test: {result.hits}/{result.shots_fired} hits ({result.accuracy:.1f}%)"

            test_result = TestResult(
                test_name="single_target_stationary",
                duration=actual_duration,
                shots_fired=result.shots_fired,
                hits=result.hits,
                hit_rate=result.accuracy,
                pigeons_spawned=1,
                success=success,
                details=details,
            )

            self.results.append(test_result)
            log.info(f"   Result: {'✅ PASS' if success else '❌ FAIL'} - {details}")

        finally:
            CONFIG.multi_target.MULTI_TARGET_ENABLED = original_multi_enabled

    def test_single_target_moving(self):
        """Test: Single moving pigeon, predictive targeting"""
        log.info("\n🎯 Test 3: Single moving target")

        original_multi_enabled = CONFIG.multi_target.MULTI_TARGET_ENABLED
        CONFIG.multi_target.MULTI_TARGET_ENABLED = False

        try:
            config = {
                "fire_cooldown": 0.2,
                "angle_threshold_close": 4.0,
                "max_engagement_distance": 1000,
            }

            headless_config = HeadlessConfig(duration=8.0, log_level="INFO")
            simulator = HeadlessTurretSimulator(config, headless_config)

            # Moving pigeon with predictable trajectory
            pigeon = Pigeon(
                position=np.array([200.0, 100.0, 600.0], dtype=float),
                velocity=np.array(
                    [-50.0, 0.0, -20.0], dtype=float
                ),  # Moving toward origin
                size=30,
                active=True,
                id=0,
                spawn_time=time.time(),
            )
            simulator.entity_manager.pigeons.append(pigeon)
            simulator.entity_manager.next_pigeon_id = 1

            start_time = time.time()
            result = simulator.run_experiment()
            actual_duration = time.time() - start_time

            # Should hit moving target with prediction
            success = result.hits >= 1
            details = f"Moving test: {result.hits}/{result.shots_fired} hits ({result.accuracy:.1f}%)"

            test_result = TestResult(
                test_name="single_target_moving",
                duration=actual_duration,
                shots_fired=result.shots_fired,
                hits=result.hits,
                hit_rate=result.accuracy,
                pigeons_spawned=1,
                success=success,
                details=details,
            )

            self.results.append(test_result)
            log.info(f"   Result: {'✅ PASS' if success else '❌ FAIL'} - {details}")

        finally:
            CONFIG.multi_target.MULTI_TARGET_ENABLED = original_multi_enabled

    def test_multi_target_limited(self):
        """Test: 5 pigeons, max 2 targets, should hit both efficiently"""
        log.info("\n🎯 Test 4: Multi-target limited (max 2)")

        original_multi_enabled = CONFIG.multi_target.MULTI_TARGET_ENABLED
        original_multi_max = CONFIG.multi_target.MULTI_TARGET_MAX
        original_min_targets = CONFIG.multi_target.MIN_TARGETS_FOR_MULTI

        CONFIG.multi_target.MULTI_TARGET_ENABLED = True
        CONFIG.multi_target.MULTI_TARGET_MAX = 2  # Limit to 2 targets
        CONFIG.multi_target.MIN_TARGETS_FOR_MULTI = 2

        try:
            config = {
                "fire_cooldown": 0.15,
                "angle_threshold_close": 4.0,
                "max_engagement_distance": 800,
            }

            headless_config = HeadlessConfig(duration=10.0, log_level="INFO")
            simulator = HeadlessTurretSimulator(config, headless_config)

            # 5 pigeons, but system should only target 2 at a time
            positions = [
                np.array([0.0, 80.0, 450.0], dtype=float),  # Center
                np.array([100.0, 90.0, 480.0], dtype=float),  # Right
                np.array([-80.0, 70.0, 460.0], dtype=float),  # Left
                np.array([150.0, 100.0, 520.0], dtype=float),  # Far right
                np.array([-120.0, 60.0, 500.0], dtype=float),  # Far left
            ]

            current_time = time.time()
            for i, pos in enumerate(positions):
                velocity = np.array(
                    [
                        np.random.uniform(-15, 15),
                        np.random.uniform(-5, 5),
                        np.random.uniform(-5, 5),
                    ],
                    dtype=float,
                )

                pigeon = Pigeon(
                    position=pos,
                    velocity=velocity,
                    size=30,
                    active=True,
                    id=i,
                    spawn_time=current_time,
                )
                simulator.entity_manager.pigeons.append(pigeon)

            simulator.entity_manager.next_pigeon_id = 5

            start_time = time.time()
            result = simulator.run_experiment()
            actual_duration = time.time() - start_time

            # Should hit both targets in limited multi-target mode
            success = result.hits >= 2 and result.accuracy > 30
            details = f"Limited multi: {result.hits}/{result.shots_fired} hits ({result.accuracy:.1f}%)"

            test_result = TestResult(
                test_name="multi_target_limited",
                duration=actual_duration,
                shots_fired=result.shots_fired,
                hits=result.hits,
                hit_rate=result.accuracy,
                pigeons_spawned=5,
                success=success,
                details=details,
            )

            self.results.append(test_result)
            log.info(f"   Result: {'✅ PASS' if success else '❌ FAIL'} - {details}")

        finally:
            CONFIG.multi_target.MULTI_TARGET_ENABLED = original_multi_enabled
            CONFIG.multi_target.MULTI_TARGET_MAX = original_multi_max
            CONFIG.multi_target.MIN_TARGETS_FOR_MULTI = original_min_targets

    def test_opportunistic_close_targets(self):
        """Test: Opportunistic algorithm with very close targets"""
        log.info("\n🎯 Test 5: Opportunistic close targets")

        original_multi_enabled = CONFIG.multi_target.MULTI_TARGET_ENABLED
        original_targeting_mode = CONFIG.targeting.TARGETING_MODE

        CONFIG.multi_target.MULTI_TARGET_ENABLED = False
        CONFIG.targeting.TARGETING_MODE = "opportunistic"

        try:
            config = {
                "fire_cooldown": 0.15,
                "angle_threshold_close": 5.0,
                "max_engagement_distance": 600,  # Shorter range for opportunistic
            }

            headless_config = HeadlessConfig(duration=8.0, log_level="INFO")
            simulator = HeadlessTurretSimulator(config, headless_config)

            # Very close, slow targets perfect for opportunistic
            close_positions = [
                np.array(
                    [50.0, 60.0, 300.0], dtype=float
                ),  # Very close, slightly right
                np.array(
                    [-30.0, 70.0, 280.0], dtype=float
                ),  # Very close, slightly left
            ]

            current_time = time.time()
            for i, pos in enumerate(close_positions):
                velocity = np.array([5.0, 0.0, 0.0], dtype=float)  # Very slow movement

                pigeon = Pigeon(
                    position=pos,
                    velocity=velocity,
                    size=30,
                    active=True,
                    id=i,
                    spawn_time=current_time,
                )
                simulator.entity_manager.pigeons.append(pigeon)

            simulator.entity_manager.next_pigeon_id = 2

            start_time = time.time()
            result = simulator.run_experiment()
            actual_duration = time.time() - start_time

            # Opportunistic should excel at close, slow targets
            success = result.shots_fired > 0 and result.hits >= 1
            details = f"Opportunistic: {result.hits}/{result.shots_fired} hits ({result.accuracy:.1f}%)"

            test_result = TestResult(
                test_name="opportunistic_close_targets",
                duration=actual_duration,
                shots_fired=result.shots_fired,
                hits=result.hits,
                hit_rate=result.accuracy,
                pigeons_spawned=2,
                success=success,
                details=details,
            )

            self.results.append(test_result)
            log.info(f"   Result: {'✅ PASS' if success else '❌ FAIL'} - {details}")

        finally:
            CONFIG.multi_target.MULTI_TARGET_ENABLED = original_multi_enabled
            CONFIG.targeting.TARGETING_MODE = original_targeting_mode

    def _print_summary(self):
        """Print test summary"""
        log.info("\n" + "=" * 50)
        log.info("📊 FOCUSED TEST SUMMARY")
        log.info("=" * 50)

        passed = sum(1 for r in self.results if r.success)
        total = len(self.results)

        log.info(f"Tests Passed: {passed}/{total}")
        log.info(f"Overall Success Rate: {passed / total * 100:.1f}%")
        log.info("")

        for result in self.results:
            status = "✅ PASS" if result.success else "❌ FAIL"
            log.info(f"{status} {result.test_name}: {result.details}")

        log.info("")
        if passed == total:
            log.info("🎉 ALL TESTS PASSED! System is working correctly.")
        elif passed > total // 2:
            log.info("⚠️  Most tests passed, but some issues remain.")
        else:
            log.info("🚨 MAJOR ISSUES: Most tests failed.")


def main():
    """Run focused regression tests"""
    tester = FocusedTester()
    results = tester.run_all_tests()

    # Return exit code based on results
    passed = sum(1 for r in results if r.success)
    return 0 if passed == len(results) else 1


if __name__ == "__main__":
    exit(main())
