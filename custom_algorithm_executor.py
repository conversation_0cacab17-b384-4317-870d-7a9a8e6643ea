"""Custom Algorithm Executor for Meta-Brain"""

import math
import numpy as np
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger as log

from entities import Pigeon, EntityManager
from camera import Camera


class CustomAlgorithmExecutor:
    """Executes custom targeting algorithms written by the meta-brain"""

    def __init__(self):
        self.custom_algorithms: Dict[str, str] = {}  # name -> code
        self.execution_results: List[Dict[str, Any]] = []

    def add_algorithm(self, name: str, code: str):
        """Add a custom algorithm to the executor"""
        self.custom_algorithms[name] = code
        log.info(f"Added custom algorithm: {name}")

    def create_algorithm_context(
        self, pigeons: List[Pigeon], camera: Camera, entity_manager: EntityManager
    ) -> Dict[str, Any]:
        """Create execution context for custom algorithms"""

        # Prepare pigeon data in a safe format
        pigeon_data = []
        for p in pigeons:
            if p.active:
                yaw, pitch, distance = p.to_spherical()
                pigeon_data.append(
                    {
                        "id": p.id,
                        "position": p.position.tolist(),
                        "velocity": p.velocity.tolist(),
                        "distance": distance,
                        "yaw": yaw,
                        "pitch": pitch,
                        "speed": np.linalg.norm(p.velocity),
                        "shots_taken": p.shots_taken_at,
                        "is_fleeing": p.is_fleeing,
                    }
                )

        # Camera state
        camera_state = {
            "yaw": camera.state.yaw,
            "pitch": camera.state.pitch,
            "fov": camera.state.fov,
            "max_speed": camera.max_speed,
        }

        # Available functions for the algorithm
        context = {
            # Data
            "pigeons": pigeon_data,
            "camera": camera_state,
            # Math functions
            "math": math,
            "np": np,
            # Helper functions
            "calculate_angle_diff": lambda yaw1, pitch1, yaw2, pitch2: math.sqrt(
                ((yaw2 - yaw1 + 180) % 360 - 180) ** 2 + (pitch2 - pitch1) ** 2
            ),
            "calculate_movement_time": lambda yaw, pitch: camera.get_movement_time(
                yaw, pitch
            ),
            "calculate_lead_time": lambda distance, speed: distance
            / 500.0,  # Projectile speed
            # Constants from config
            "MAX_ENGAGEMENT_DISTANCE": 1200,
            "PROJECTILE_SPEED": 500,
            "ANGLE_THRESHOLD_CLOSE": 3.0,
            "ANGLE_THRESHOLD_FAR": 8.0,
        }

        return context

    def execute_algorithm(
        self,
        name: str,
        pigeons: List[Pigeon],
        camera: Camera,
        entity_manager: EntityManager,
    ) -> Optional[Dict[str, Any]]:
        """Execute a custom algorithm and return targeting decision"""
        if name not in self.custom_algorithms:
            log.error(f"Algorithm {name} not found")
            return None

        code = self.custom_algorithms[name]
        context = self.create_algorithm_context(pigeons, camera, entity_manager)

        try:
            # Create a restricted execution environment
            exec_globals = {
                "__builtins__": {
                    "len": len,
                    "min": min,
                    "max": max,
                    "abs": abs,
                    "sorted": sorted,
                    "range": range,
                    "enumerate": enumerate,
                    "float": float,
                    "int": int,
                    "str": str,
                    "list": list,
                    "dict": dict,
                    "print": print,  # For debugging
                }
            }
            exec_globals.update(context)

            # Execute the algorithm
            exec(code, exec_globals)

            # The algorithm should define a select_target function
            if "select_target" not in exec_globals:
                log.error(f"Algorithm {name} does not define select_target function")
                return None

            # Call the custom select_target function
            result = exec_globals["select_target"]()

            # Validate result
            if result and isinstance(result, dict):
                required_keys = [
                    "target_id",
                    "target_yaw",
                    "target_pitch",
                    "confidence",
                ]
                if all(key in result for key in required_keys):
                    # Record execution
                    self.execution_results.append(
                        {
                            "algorithm": name,
                            "timestamp": datetime.now().isoformat(),
                            "result": result,
                            "pigeon_count": len(pigeons),
                        }
                    )
                    return result
                else:
                    log.error(f"Algorithm {name} returned incomplete result: {result}")

        except Exception as e:
            log.error(f"Error executing algorithm {name}: {e}")
            self.execution_results.append(
                {
                    "algorithm": name,
                    "timestamp": datetime.now().isoformat(),
                    "error": str(e),
                    "pigeon_count": len(pigeons),
                }
            )

        return None

    def generate_algorithm_template(self) -> str:
        """Generate a template for custom algorithms"""
        template = '''# Custom Targeting Algorithm Template
# Available variables:
# - pigeons: List of pigeon data with keys: id, position, velocity, distance, yaw, pitch, speed, shots_taken, is_fleeing
# - camera: Camera state with keys: yaw, pitch, fov, max_speed
# - math, np: Math libraries
# - Helper functions: calculate_angle_diff, calculate_movement_time, calculate_lead_time

def select_target():
    """Select best target from available pigeons
    
    Returns:
        dict with keys: target_id, target_yaw, target_pitch, confidence
        or None if no suitable target
    """
    if not pigeons:
        return None
    
    best_score = -float('inf')
    best_target = None
    
    for pigeon in pigeons:
        # Skip fleeing pigeons or those too far away
        if pigeon['is_fleeing'] or pigeon['distance'] > MAX_ENGAGEMENT_DISTANCE:
            continue
            
        # Calculate score based on your custom logic
        # Example: Prefer close, slow-moving targets
        distance_score = 1.0 / (1.0 + pigeon['distance'] / 500)
        speed_score = 1.0 / (1.0 + pigeon['speed'] / 100)
        
        # Consider angle to target
        angle_diff = calculate_angle_diff(
            camera['yaw'], camera['pitch'],
            pigeon['yaw'], pigeon['pitch']
        )
        angle_score = 1.0 / (1.0 + angle_diff / 30)
        
        # Combined score
        score = distance_score * 2.0 + speed_score * 1.5 + angle_score * 1.0
        
        if score > best_score:
            best_score = score
            
            # Calculate lead for moving targets
            lead_time = calculate_lead_time(pigeon['distance'], pigeon['speed'])
            future_pos = [
                pigeon['position'][0] + pigeon['velocity'][0] * lead_time,
                pigeon['position'][1] + pigeon['velocity'][1] * lead_time,
                pigeon['position'][2] + pigeon['velocity'][2] * lead_time
            ]
            
            # Convert future position to angles (simplified)
            # In real implementation, use proper spherical coordinate conversion
            dist = np.linalg.norm(future_pos)
            future_yaw = math.degrees(math.atan2(future_pos[0], future_pos[2])) % 360
            future_pitch = math.degrees(math.asin(np.clip(future_pos[1] / dist, -1, 1)))
            
            best_target = {
                'target_id': pigeon['id'],
                'target_yaw': future_yaw,
                'target_pitch': future_pitch,
                'confidence': min(score / 3.0, 1.0)
            }
    
    return best_target
'''
        return template
