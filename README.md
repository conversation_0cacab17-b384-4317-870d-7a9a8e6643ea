# Pigeon Turret Simulator

An automated anti-pigeon turret simulation with 3D environment, multiple targeting algorithms, and performance evaluation.

## Features

### 1. **3D Environment Simulation**
- 360-degree panoramic view with FPS-style camera controls
- Click-to-point targeting with lens distortion calculations
- Support for real 360° panorama images (place in `assets/balcony_360.jpg`)
- Default procedural background if no panorama available

### 2. **Targeting System**
- **Three targeting algorithms:**
  - **Nearest**: Targets the closest pigeon
  - **Predictive**: Calculates intercept points for moving targets
  - **Opportunistic**: Targets pigeons requiring minimal turret movement
- Real-time algorithm switching for comparison
- Automated targeting and firing system

### 3. **Performance Evaluation**
- Real-time accuracy tracking
- Movement time and reaction time metrics
- Per-algorithm performance comparison
- Session statistics with JSON export
- Performance plots (if matplotlib available)

### 4. **Interactive Controls**
- **Mouse:**
  - Left-click: Point camera to location
- **Keyboard:**
  - `SPACE`: Pause/Resume
  - `D`: Toggle debug info
  - `M`: Toggle manual control mode
  - `A`: Toggle auto-fire
  - `F`: Fire (manual mode only)
  - `1/2/3`: Switch algorithms (Nearest/Predictive/Opportunistic)
  - `Arrow Keys`: Manual camera control (manual mode)
  - `ESC`: Exit

## Installation

1. **Install UV package manager:**
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

2. **Install dependencies:**
```bash
uv sync
```

## Running the Simulation

```bash
uv run main.py
```

## Configuration

Edit `config.py` to adjust:
- Window size and FPS
- Camera FOV and movement speeds
- Pigeon spawn rates and speeds
- Projectile speed and hit radius
- Targeting algorithm settings

## Adding a 360° Panorama

Place a 360-degree panorama image at `assets/balcony_360.jpg`. The image should be in equirectangular projection format (2:1 aspect ratio).

## Output Files

After running, the simulator creates:
- `turret_metrics.json`: Detailed performance metrics
- `turret_performance.png`: Performance comparison charts (if matplotlib available)

## Architecture

- **`main.py`**: Main application and game loop
- **`camera.py`**: 360-degree camera system with smooth movement
- **`entities.py`**: Pigeons and projectiles with physics
- **`environment.py`**: 3D rendering and panorama display
- **`targeting.py`**: Targeting algorithms implementation
- **`evaluation.py`**: Performance tracking and metrics
- **`config.py`**: All configuration parameters

## Performance Metrics

The simulator tracks:
- **Accuracy**: Hit rate percentage
- **Movement Time**: Average time to aim at targets
- **Reaction Time**: Time from detection to firing
- **Algorithm Comparison**: Side-by-side performance analysis

## Tips

- Start in auto mode to see the targeting algorithms in action
- Switch between algorithms (keys 1-3) to compare performance
- Use manual mode to test your own targeting skills
- Check the performance report after exiting for detailed statistics
